<?php
/**
 * Theme CSS and Logo Include for User Pages
 *
 * This file includes the custom theme CSS and logo from admin appearance settings
 * Include this file in the <head> section of user pages after Bootstrap CSS
 */

// Include config to access settings functions
if (!function_exists('get_site_setting')) {
    require_once '../config.php';
}

// Load custom theme CSS if it exists
$customThemeFile = '../admin/css/custom-theme.css';
if (file_exists($customThemeFile)): ?>
    <link rel="stylesheet" href="<?php echo '../admin/css/custom-theme.css?t=' . filemtime($customThemeFile); ?>">
<?php endif; ?>

<!-- Also check for cached theme CSS -->
<?php
$cachedThemeFile = '../cache/theme-cache.css';
if (file_exists($cachedThemeFile) && !file_exists($customThemeFile)): ?>
    <link rel="stylesheet" href="<?php echo '../cache/theme-cache.css?t=' . filemtime($cachedThemeFile); ?>">
<?php endif; ?>

<!-- Load favicon if available -->
<?php
$favicon_path = get_site_setting('favicon_path', '');
if ($favicon_path && file_exists('../' . $favicon_path)): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo '../' . $favicon_path; ?>">
<?php endif; ?>

<script>
// Load logo and organization name dynamically
document.addEventListener('DOMContentLoaded', function() {
    // Get logo and organization info
    <?php
    // Check for header logo first (preferred for navbar), then main logo
    $header_logo_path = get_site_setting('header_logo', '');
    $main_logo_path = get_site_setting('main_logo', '');
    $logo_path = '';

    if ($header_logo_path && file_exists('../' . $header_logo_path)) {
        $logo_path = $header_logo_path;
    } elseif ($main_logo_path && file_exists('../' . $main_logo_path)) {
        $logo_path = $main_logo_path;
    }

    $organization_name = get_organization_name();
    ?>

    const logoPath = '<?php echo $logo_path ? '../' . $logo_path : ''; ?>';
    const orgName = '<?php echo htmlspecialchars($organization_name, ENT_QUOTES); ?>';

    // Update navbar logo and title
    const navbarBrand = document.querySelector('.navbar-brand');
    if (navbarBrand) {
        // Add logo if available
        if (logoPath) {
            // Check if logo image already exists
            let logoImg = navbarBrand.querySelector('img');
            if (!logoImg) {
                logoImg = document.createElement('img');
                logoImg.style.height = '40px';
                logoImg.style.marginRight = '10px';
                logoImg.alt = 'Logo';
                navbarBrand.insertBefore(logoImg, navbarBrand.firstChild);
            }
            logoImg.src = logoPath;
            logoImg.style.display = 'inline-block';
        }

        // Update organization name - handle different navbar structures
        if (orgName) {
            // Method 1: Look for span element (dashboard style)
            const navbarTitle = navbarBrand.querySelector('span');
            if (navbarTitle) {
                navbarTitle.textContent = orgName;
            } else {
                // Method 2: Update text content directly (events style)
                // Get current text content and replace the site name part
                const currentText = navbarBrand.textContent || navbarBrand.innerText;
                if (currentText) {
                    // Replace common site names
                    const updatedText = currentText
                        .replace('Church Management System', orgName)
                        .replace('Freedom Assembly Church', orgName)
                        .replace(/^\s*\w+\s+/, ''); // Remove icon text if present

                    // Update the text content while preserving the icon
                    const icon = navbarBrand.querySelector('i');
                    if (icon) {
                        navbarBrand.innerHTML = '';
                        navbarBrand.appendChild(icon);
                        navbarBrand.appendChild(document.createTextNode(' ' + orgName));
                    } else {
                        navbarBrand.textContent = orgName;
                    }
                }
            }
        }
    }

    // Update page title
    if (orgName && document.title.includes('Freedom Assembly Church')) {
        document.title = document.title.replace('Freedom Assembly Church', orgName);
    }
});
</script>
