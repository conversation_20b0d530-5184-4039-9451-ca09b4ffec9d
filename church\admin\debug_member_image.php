<?php
require_once '../config.php';

// Get member ID from URL
$member_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($member_id <= 0) {
    die("Please provide a valid member ID: debug_member_image.php?id=X");
}

try {
    // Get member data
    $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$member_id]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        die("Member not found with ID: $member_id");
    }
    
    echo "<h2>Debug Member Image for: " . htmlspecialchars($member['full_name']) . "</h2>";
    echo "<p><strong>Member ID:</strong> $member_id</p>";
    echo "<p><strong>Database image_path:</strong> " . htmlspecialchars($member['image_path'] ?? 'NULL') . "</p>";
    
    // Test path resolution logic
    $profileImagePath = '';
    $showImage = false;
    
    if (!empty($member['image_path'])) {
        $dbImagePath = $member['image_path'];
        $testPaths = [];
        
        echo "<h3>Testing Path Resolution:</h3>";
        echo "<p><strong>Original DB Path:</strong> " . htmlspecialchars($dbImagePath) . "</p>";
        
        // Try multiple possible path formats for old vs new users
        if (strpos($dbImagePath, 'uploads/') === 0) {
            // Path is 'uploads/filename.jpg' - add '../'
            $testPaths[] = '../' . $dbImagePath;
            echo "<p>✓ Detected 'uploads/' prefix format</p>";
        } elseif (strpos($dbImagePath, '/uploads/') !== false) {
            // Path contains '/uploads/' - use as is with '..'
            $testPaths[] = '..' . $dbImagePath;
            echo "<p>✓ Detected '/uploads/' format</p>";
        } else {
            // Try different combinations for old formats
            $testPaths[] = '../uploads/' . basename($dbImagePath);
            $testPaths[] = '../' . $dbImagePath;
            $testPaths[] = $dbImagePath; // In case it's already relative
            echo "<p>✓ Using fallback path combinations</p>";
        }
        
        echo "<h4>Testing Paths:</h4>";
        echo "<ul>";
        
        // Test each possible path until we find one that exists
        foreach ($testPaths as $testPath) {
            $fullPath = __DIR__ . '/' . $testPath;
            $exists = file_exists($fullPath);
            echo "<li>";
            echo "<strong>Test Path:</strong> " . htmlspecialchars($testPath) . "<br>";
            echo "<strong>Full Path:</strong> " . htmlspecialchars($fullPath) . "<br>";
            echo "<strong>File Exists:</strong> " . ($exists ? "✅ YES" : "❌ NO") . "<br>";
            
            if ($exists) {
                $profileImagePath = $testPath;
                $showImage = true;
                echo "<strong>🎉 USING THIS PATH!</strong>";
                break;
            }
            echo "</li><br>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ No image_path in database</p>";
    }
    
    echo "<h3>Final Result:</h3>";
    echo "<p><strong>Show Image:</strong> " . ($showImage ? "✅ YES" : "❌ NO") . "</p>";
    echo "<p><strong>Final Path:</strong> " . htmlspecialchars($profileImagePath) . "</p>";
    
    if ($showImage) {
        echo "<h4>Image Preview:</h4>";
        echo "<img src='" . htmlspecialchars($profileImagePath) . "' alt='Preview' style='max-width: 200px; border: 2px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"IMAGE FAILED TO LOAD\";'>";
        echo "<p><em>If you see a broken image above, the path resolution worked but the file is corrupted or inaccessible.</em></p>";
    }
    
    // List upload directory contents
    echo "<h3>Upload Directory Contents:</h3>";
    $uploadDir = __DIR__ . '/../uploads/';
    if (is_dir($uploadDir)) {
        $files = scandir($uploadDir);
        echo "<ul>";
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                echo "<li>" . htmlspecialchars($file) . "</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p>❌ Upload directory not found: " . htmlspecialchars($uploadDir) . "</p>";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . htmlspecialchars($e->getMessage());
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
p, li { margin: 5px 0; }
ul { margin: 10px 0; }
</style>
