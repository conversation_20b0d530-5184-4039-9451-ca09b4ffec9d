<?php
// Test all shortcodes in birthday templates
require_once 'config.php';

echo "<h1>Shortcode Validation Test</h1>\n";

// Sample member data for testing
$sampleMember = [
    'full_name' => '<PERSON>',
    'first_name' => '<PERSON>',
    'last_name' => '<PERSON>e',
    'email' => '<EMAIL>',
    'phone_number' => '+1234567890',
    'birth_date' => '1990-06-21',
    'image_path' => 'uploads/sample-profile.jpg',
    'home_address' => '123 Main St, City, State',
    'occupation' => 'Software Developer'
];

// Sample birthday member data
$birthdayMember = [
    'birthday_member_name' => '<PERSON>',
    'birthday_member_first_name' => '<PERSON>',
    'birthday_member_full_name' => '<PERSON>',
    'birthday_member_email' => '<EMAIL>',
    'birthday_member_phone' => '+1987654321',
    'birthday_member_age' => 25,
    'birthday_member_image' => 'uploads/jane-profile.jpg',
    'birthday_member_photo_url' => get_base_url() . '/uploads/jane-profile.jpg',
    'birthday_member_birth_date' => 'June 21',
    'upcoming_birthday_date' => 'June 21, 2025',
    'upcoming_birthday_formatted' => 'Saturday, June 21, 2025',
    'upcoming_birthday_day' => 'Saturday',
    'days_text' => 'today',
    'days_until_birthday' => 0
];

// Merge the data
$testData = array_merge($sampleMember, $birthdayMember);

// Test content with various shortcodes
$testContent = "
<h2>Testing All Shortcodes</h2>

<h3>Member Information:</h3>
- Full Name: {full_name}
- First Name: {first_name}
- Last Name: {last_name}
- Email: {email}
- Phone: {phone_number}
- Member Image: {member_image}

<h3>Birthday Member Information:</h3>
- Birthday Member Name: {birthday_member_name}
- Birthday Member First Name: {birthday_member_first_name}
- Birthday Member Full Name: {birthday_member_full_name}
- Birthday Member Email: {birthday_member_email}
- Birthday Member Phone: {birthday_member_phone}
- Birthday Member Age: {birthday_member_age}
- Birthday Member Image: {birthday_member_image}
- Birthday Member Photo URL: {birthday_member_photo_url}

<h3>Date Information:</h3>
- Upcoming Birthday Date: {upcoming_birthday_date}
- Upcoming Birthday Formatted: {upcoming_birthday_formatted}
- Upcoming Birthday Day: {upcoming_birthday_day}
- Days Text: {days_text}
- Days Until Birthday: {days_until_birthday}

<h3>Organization Information:</h3>
- Church Name: {church_name}
- Church Logo: {church_logo}
- Site URL: {site_url}
- Church Address: {church_address}
- Church Phone: {church_phone}
- Church Email: {church_email}

<h3>Recipient Information:</h3>
- Recipient Full Name: {recipient_full_name}
- Recipient First Name: {recipient_first_name}
- Recipient Email: {recipient_email}

<h3>Date/Time:</h3>
- Current Year: {current_year}
- Current Date: {current_date}
- Current Time: {current_time}
";

echo "<h2>Before Replacement:</h2>\n";
echo "<pre>" . htmlspecialchars($testContent) . "</pre>\n";

// Process the content through the placeholder replacement function
$processedContent = replaceTemplatePlaceholders($testContent, $testData);

echo "<h2>After Replacement:</h2>\n";
echo "<div style='border: 1px solid #ccc; padding: 15px; background: #f9f9f9;'>\n";
echo $processedContent;
echo "</div>\n";

// Check for any unreplaced placeholders
preg_match_all('/{([^}]+)}/', $processedContent, $matches);

if (!empty($matches[0])) {
    echo "<h2 style='color: red;'>Unreplaced Shortcodes Found:</h2>\n";
    echo "<ul>\n";
    foreach (array_unique($matches[0]) as $unreplaced) {
        echo "<li style='color: red;'>" . htmlspecialchars($unreplaced) . "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<h2 style='color: green;'>✓ All shortcodes were successfully replaced!</h2>\n";
}

// Test birthday templates specifically
echo "<hr><h2>Testing Birthday Templates:</h2>\n";

try {
    $stmt = $pdo->query("SELECT id, template_name, subject, content FROM email_templates WHERE is_birthday_template = 1 LIMIT 3");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($templates as $template) {
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";
        
        // Test subject
        $processedSubject = replaceTemplatePlaceholders($template['subject'], $testData);
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($processedSubject) . "</p>\n";
        
        // Check for unreplaced placeholders in subject
        preg_match_all('/{([^}]+)}/', $processedSubject, $subjectMatches);
        if (!empty($subjectMatches[0])) {
            echo "<p style='color: red;'>Unreplaced in subject: " . implode(', ', $subjectMatches[0]) . "</p>\n";
        }
        
        // Test content (show first 500 chars)
        $processedContent = replaceTemplatePlaceholders($template['content'], $testData);
        echo "<p><strong>Content Preview:</strong></p>\n";
        echo "<div style='border: 1px solid #ddd; padding: 10px; max-height: 200px; overflow-y: auto; font-size: 12px;'>\n";
        echo htmlspecialchars(substr($processedContent, 0, 500)) . "...\n";
        echo "</div>\n";
        
        // Check for unreplaced placeholders in content
        preg_match_all('/{([^}]+)}/', $processedContent, $contentMatches);
        if (!empty($contentMatches[0])) {
            echo "<p style='color: red;'>Unreplaced in content: " . implode(', ', array_unique($contentMatches[0])) . "</p>\n";
        } else {
            echo "<p style='color: green;'>✓ All shortcodes replaced in this template</p>\n";
        }
        
        echo "<hr>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing templates: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Test Complete</h2>\n";
?>
