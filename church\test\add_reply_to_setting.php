<?php
// <PERSON><PERSON><PERSON> to ensure the reply_to_email setting exists in the email_settings table
require_once __DIR__ . '/../config.php';

// Database connection
$conn = $pdo;

try {
    // Check if reply_to_email setting already exists in email_settings
    $stmt = $conn->prepare("SELECT id FROM email_settings WHERE setting_key = 'reply_to_email'");
    $stmt->execute();
    $exists = $stmt->fetch();
    
    if (!$exists) {
        // Get sender_email as default value if available
        $default_email = '<EMAIL>'; // Default fallback
        
        $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'sender_email'");
        $stmt->execute();
        $sender = $stmt->fetch();
        
        if ($sender && !empty($sender['setting_value'])) {
            $default_email = $sender['setting_value'];
        }
        
        // Insert the reply_to_email setting
        $stmt = $conn->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES ('reply_to_email', ?)");
        $stmt->execute([$default_email]);
        
        echo "<h3>Success!</h3>";
        echo "<p>Added reply_to_email setting with value: " . htmlspecialchars($default_email) . "</p>";
    } else {
        // Setting already exists, just show current value
        $stmt = $conn->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email'");
        $stmt->execute();
        $setting = $stmt->fetch();
        
        echo "<h3>Setting Already Exists</h3>";
        echo "<p>Current reply_to_email value: " . htmlspecialchars($setting['setting_value']) . "</p>";
    }
    
    // Show link to admin settings
    echo "<p><a href='../admin/email_settings.php'>Go to Email Settings</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
} 