<?php
/**
 * Forgot Password Page
 * 
 * Allows users to request password reset via email
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../includes/email_functions.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Redirect if already logged in
if ($userAuth->isAuthenticated()) {
    header("Location: dashboard.php");
    exit();
}

// Process forgot password form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_password'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $identifier = $security->sanitizeInput($_POST['identifier'], 'text');
        
        if (empty($identifier)) {
            $error = "Please enter your email address or phone number.";
        } else {
            $result = $userAuth->generatePasswordResetToken($identifier);
            
            if ($result['success']) {
                // Send password reset email
                try {
                    $resetLink = "http" . (isset($_SERVER['HTTPS']) ? "s" : "") . "://" . $_SERVER['HTTP_HOST'] . 
                                dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $result['token'];
                    
                    $to = $result['user']['email'];
                    $toName = $result['user']['full_name'];
                    $subject = 'Password Reset Request';
                    $body = "
                        <h2>Password Reset Request</h2>
                        <p>Hello {$result['user']['full_name']},</p>
                        <p>You have requested to reset your password. Click the link below to reset your password:</p>
                        <p><a href='$resetLink' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
                        <p>If you did not request this password reset, please ignore this email.</p>
                        <p>This link will expire in 4 hours for security reasons.</p>
                        <p>Best regards,<br>Church Management Team</p>
                    ";

                    if (sendEmail($to, $toName, $subject, $body, true, $result['user'])) {
                        $success = "Password reset instructions have been sent to your email address.";
                    } else {
                        $error = "Failed to send password reset email. Please try again or contact support.";
                    }
                } catch (Exception $e) {
                    error_log("Password reset email error: " . $e->getMessage());
                    $error = "An error occurred while sending the reset email. Please try again.";
                }
            } else {
                $error = $result['message'];
            }
        }
    }
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .forgot-password-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .forgot-password-logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .forgot-password-logo h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .forgot-password-logo p {
            color: #7f8c8d;
            font-size: 1rem;
            margin: 0;
        }
        
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #fee;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            color: #363;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .forgot-password-links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .forgot-password-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .forgot-password-links a:hover {
            color: #764ba2;
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 12px 0 0 12px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }
        
        .input-group:focus-within .input-group-text {
            border-color: #667eea;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .info-box h6 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        
        .info-box p {
            color: #1565c0;
            margin: 0;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="forgot-password-logo">
            <h1><i class="bi bi-key"></i> Reset Password</h1>
            <p><?php echo htmlspecialchars($sitename); ?></p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                <div class="mt-2">
                    <a href="login.php" class="btn btn-success btn-sm">
                        <i class="bi bi-box-arrow-in-right"></i> Back to Login
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="info-box">
                <h6><i class="bi bi-info-circle"></i> Password Reset</h6>
                <p>Enter your email address or phone number and we'll send you instructions to reset your password.</p>
            </div>
            
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                <?php echo $security->generateCSRFInput(); ?>
                
                <div class="mb-3">
                    <label for="identifier" class="form-label">
                        <i class="bi bi-envelope"></i> Email or Phone Number
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               id="identifier" 
                               name="identifier" 
                               placeholder="Enter your email or phone number"
                               required 
                               autocomplete="username">
                    </div>
                    <div class="help-text">
                        We'll send reset instructions to your registered email address
                    </div>
                </div>
                
                <div class="d-grid gap-2 mb-3">
                    <button type="submit" name="reset_password" class="btn btn-primary">
                        <i class="bi bi-envelope"></i> Send Reset Instructions
                    </button>
                </div>
            </form>
        <?php endif; ?>
        
        <div class="forgot-password-links">
            <div class="mb-2">
                <a href="login.php">
                    <i class="bi bi-arrow-left"></i> Back to Login
                </a>
            </div>
            <div class="mb-2">
                <a href="register.php">
                    <i class="bi bi-person-plus"></i> Create new account
                </a>
            </div>
            <div>
                <a href="../register.php">
                    <i class="bi bi-house"></i> Back to homepage
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on identifier field
        document.getElementById('identifier').focus();
    </script>
</body>
</html>
