<?php
/**
 * Check Actual Birthday Template Content
 */

require_once 'config.php';

echo "<h2>🔍 Check Actual Birthday Template</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .template { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
    .content { background: #fff; padding: 10px; border-radius: 3px; margin: 5px 0; max-height: 400px; overflow-y: auto; }
    .highlight { background: yellow; padding: 2px 4px; border-radius: 2px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
</style>";

try {
    // Get all birthday templates
    $stmt = $pdo->query("SELECT id, template_name, subject, content FROM email_templates WHERE is_birthday_template = 1 ORDER BY id");
    $templates = $stmt->fetchAll();
    
    if ($templates) {
        echo "<h3>📧 All Birthday Templates:</h3>";
        
        foreach ($templates as $template) {
            echo "<div class='template'>";
            echo "<h4>Template: {$template['template_name']} (ID: {$template['id']})</h4>";
            echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
            
            $content = $template['content'];
            
            // Check for image-related content
            $imageChecks = [
                '{member_image}' => strpos($content, '{member_image}') !== false,
                '{birthday_member_image}' => strpos($content, '{birthday_member_image}') !== false,
                '{birthday_member_photo_url}' => strpos($content, '{birthday_member_photo_url}') !== false,
                '{member_image_url}' => strpos($content, '{member_image_url}') !== false,
            ];
            
            echo "<p><strong>Image Placeholders Found:</strong></p>";
            echo "<ul>";
            foreach ($imageChecks as $placeholder => $found) {
                if ($found) {
                    echo "<li class='success'>✅ $placeholder</li>";
                } else {
                    echo "<li class='error'>❌ $placeholder</li>";
                }
            }
            echo "</ul>";
            
            // Look for circular image areas or member name displays
            if (strpos($content, 'border-radius') !== false || strpos($content, 'circular') !== false) {
                echo "<p class='success'>✅ Contains circular styling</p>";
            }
            
            // Check for member name in image context
            if (preg_match('/alt=["\'][^"\']*{[^}]*name[^}]*}[^"\']*["\']/', $content)) {
                echo "<p class='success'>✅ Has member name in alt text</p>";
            }
            
            // Show template content with highlighted placeholders
            $highlightedContent = $content;
            foreach (array_keys($imageChecks) as $placeholder) {
                $highlightedContent = str_replace($placeholder, "<span class='highlight'>$placeholder</span>", $highlightedContent);
            }
            
            echo "<div class='content'>";
            echo "<strong>Template Content:</strong><br>";
            echo $highlightedContent;
            echo "</div>";
            
            echo "</div>";
        }
        
    } else {
        echo "<p class='error'>❌ No birthday templates found</p>";
    }
    
    // Check recent birthday messages to see which template was used
    echo "<div class='template'>";
    echo "<h3>📨 Recent Birthday Messages:</h3>";
    
    $stmt = $pdo->query("
        SELECT ubm.*, m.full_name as recipient_name, et.template_name, et.content as template_content
        FROM user_birthday_messages ubm 
        LEFT JOIN members m ON ubm.recipient_id = m.id 
        LEFT JOIN email_templates et ON ubm.template_id = et.id
        WHERE ubm.sent_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        ORDER BY ubm.sent_at DESC 
        LIMIT 5
    ");
    $recentMessages = $stmt->fetchAll();
    
    if ($recentMessages) {
        foreach ($recentMessages as $msg) {
            echo "<h4>Message to: {$msg['recipient_name']} (Template: {$msg['template_name']})</h4>";
            echo "<p><strong>Sent:</strong> {$msg['sent_at']}</p>";
            echo "<p><strong>Template ID:</strong> {$msg['template_id']}</p>";
            
            if ($msg['template_content']) {
                // Check what image placeholders this template has
                $content = $msg['template_content'];
                $imageChecks = [
                    '{member_image}' => strpos($content, '{member_image}') !== false,
                    '{birthday_member_photo_url}' => strpos($content, '{birthday_member_photo_url}') !== false,
                ];
                
                echo "<p><strong>This template contains:</strong></p>";
                foreach ($imageChecks as $placeholder => $found) {
                    if ($found) {
                        echo "<span class='success'>✅ $placeholder</span> ";
                    }
                }
                echo "</p>";
            }
            echo "<hr>";
        }
    } else {
        echo "<p>No recent birthday messages found</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>🎯 Check Complete</h3>";
?>
