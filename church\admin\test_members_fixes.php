<?php
/**
 * Test Members Page Fixes
 * 
 * This script tests all the fixes applied to the members page:
 * 1. Member term setting (sidebar showing "Members" instead of "s")
 * 2. Profile image paths
 * 3. Bulk selection functionality
 * 4. Member profile display
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test Members Page Fixes</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-success text-white'>
                    <h5 class='mb-0'><i class='bi bi-check-circle'></i> Members Page Fixes Test Results</h5>
                </div>
                <div class='card-body'>
";

$allTestsPassed = true;

try {
    // Test 1: Member Term Setting
    echo "<h6><i class='bi bi-1-circle'></i> Testing Member Term Setting</h6>";
    
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'member_term'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result && !empty(trim($result['setting_value']))) {
        $memberTerm = trim($result['setting_value']);
        echo "<div class='alert alert-success'>✓ member_term setting: '$memberTerm'</div>";
        echo "<div class='alert alert-info'>
            <strong>Function Tests:</strong><br>
            • get_member_term(): '" . htmlspecialchars(get_member_term()) . "'<br>
            • get_member_term(true): '" . htmlspecialchars(get_member_term(true)) . "'
        </div>";
        
        if (get_member_term(true) !== 's') {
            echo "<div class='alert alert-success'>✓ Sidebar should now show '" . htmlspecialchars(get_member_term(true)) . "' instead of 's'</div>";
        } else {
            echo "<div class='alert alert-danger'>✗ Still showing 's' in sidebar</div>";
            $allTestsPassed = false;
        }
    } else {
        echo "<div class='alert alert-danger'>✗ member_term setting is still empty</div>";
        $allTestsPassed = false;
    }
    
    // Test 2: Profile Image Paths
    echo "<hr><h6><i class='bi bi-2-circle'></i> Testing Profile Image Paths</h6>";
    
    // Check if default profile image exists
    $defaultImagePath = __DIR__ . '/../assets/img/default-profile.jpg';
    if (file_exists($defaultImagePath)) {
        echo "<div class='alert alert-success'>✓ Default profile image exists: ../assets/img/default-profile.jpg</div>";
    } else {
        echo "<div class='alert alert-danger'>✗ Default profile image missing: ../assets/img/default-profile.jpg</div>";
        $allTestsPassed = false;
    }
    
    // Check members with image paths
    $stmt = $conn->prepare("SELECT id, full_name, image_path FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 5");
    $stmt->execute();
    $membersWithImages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($membersWithImages) > 0) {
        echo "<div class='alert alert-info'>Found " . count($membersWithImages) . " members with profile images:</div>";
        echo "<ul class='list-group mb-3'>";
        foreach ($membersWithImages as $member) {
            $imagePath = $member['image_path'];
            
            // Test the path logic
            $finalPath = '';
            if (strpos($imagePath, 'uploads/') === 0) {
                $finalPath = '../' . $imagePath;
            } elseif (strpos($imagePath, '/uploads/') !== false) {
                $finalPath = '..' . $imagePath;
            } else {
                $finalPath = '../uploads/' . ltrim($imagePath, '/');
            }
            
            $fullPath = __DIR__ . '/' . $finalPath;
            $exists = file_exists($fullPath);
            
            echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
            echo htmlspecialchars($member['full_name']) . " - " . htmlspecialchars($imagePath);
            echo "<span class='badge bg-" . ($exists ? 'success' : 'danger') . "'>" . ($exists ? 'EXISTS' : 'MISSING') . "</span>";
            echo "</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='alert alert-info'>No members with profile images found in database</div>";
    }
    
    // Test 3: Database Structure
    echo "<hr><h6><i class='bi bi-3-circle'></i> Testing Database Structure</h6>";
    
    // Check members table structure
    $stmt = $conn->prepare("DESCRIBE members");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = ['id', 'full_name', 'email', 'phone_number', 'birth_date', 'image_path', 'created_at'];
    $foundColumns = array_column($columns, 'Field');
    
    $missingColumns = array_diff($requiredColumns, $foundColumns);
    if (empty($missingColumns)) {
        echo "<div class='alert alert-success'>✓ All required columns exist in members table</div>";
    } else {
        echo "<div class='alert alert-danger'>✗ Missing columns in members table: " . implode(', ', $missingColumns) . "</div>";
        $allTestsPassed = false;
    }
    
    // Test 4: Sample Member Data
    echo "<hr><h6><i class='bi bi-4-circle'></i> Testing Sample Member Data</h6>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM members");
    $stmt->execute();
    $memberCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<div class='alert alert-info'>Total members in database: $memberCount</div>";
    
    if ($memberCount > 0) {
        // Get a sample member
        $stmt = $conn->prepare("SELECT * FROM members LIMIT 1");
        $stmt->execute();
        $sampleMember = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<div class='alert alert-success'>✓ Sample member data available</div>";
        echo "<div class='card mt-3'>";
        echo "<div class='card-header'>Sample Member Data</div>";
        echo "<div class='card-body'>";
        echo "<strong>Name:</strong> " . htmlspecialchars($sampleMember['full_name']) . "<br>";
        echo "<strong>Email:</strong> " . htmlspecialchars($sampleMember['email']) . "<br>";
        echo "<strong>Phone:</strong> " . htmlspecialchars($sampleMember['phone_number'] ?? 'N/A') . "<br>";
        echo "<strong>Birth Date:</strong> " . htmlspecialchars($sampleMember['birth_date'] ?? 'N/A') . "<br>";
        echo "<strong>Image Path:</strong> " . htmlspecialchars($sampleMember['image_path'] ?? 'None') . "<br>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>No members found in database - you may need to add some test data</div>";
    }
    
    // Test 5: JavaScript Functionality Test
    echo "<hr><h6><i class='bi bi-5-circle'></i> JavaScript Functionality Test</h6>";
    echo "<div class='alert alert-info'>
        <strong>Manual Tests Required:</strong><br>
        • Visit the <a href='members.php' class='alert-link'>Members page</a> and verify:<br>
        • Sidebar shows 'Members' instead of 's'<br>
        • Profile images display correctly or show default image<br>
        • Bulk selection checkboxes work<br>
        • Clear button properly deselects all members<br>
        • Member profile links work correctly
    </div>";
    
    // Final Results
    echo "<hr>";
    if ($allTestsPassed) {
        echo "<div class='alert alert-success'>
            <h6>🎉 All Automated Tests Passed!</h6>
            <p class='mb-0'>The members page fixes have been successfully applied. Please test the manual functionality on the actual members page.</p>
        </div>";
    } else {
        echo "<div class='alert alert-warning'>
            <h6>⚠️ Some Issues Found</h6>
            <p class='mb-0'>Please review the test results above and address any remaining issues.</p>
        </div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Database Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    $allTestsPassed = false;
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='members.php' class='btn btn-primary'>Test Members Page</a>
                        <a href='fix_member_term.php' class='btn btn-outline-secondary'>Run Member Term Fix</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
