# Settings Organization Guide

## Overview
The settings system has been reorganized to eliminate duplication and provide clear separation of concerns between different types of configurations.

## File Structure

### SECURITY SETTINGS ORGANIZATION

### 1. Security_settings.php - Comprehensive Security Configuration
**Location:** `church/admin/security_settings.php`

**Purpose:** Complete security management with advanced features

**Settings Managed:**
- **Password Policy:** Min length, uppercase/lowercase/numbers/special chars required, expiry days
- **Login Security:** Max attempts, lockout duration, session timeout
- **Two-Factor Authentication:** Admin 2FA requirements
- **System Security:** Audit log retention, CSRF token expiry
- **Security Features:** Account lockout protection, password strength enforcement

**Features:**
- Dedicated security_settings database table
- Advanced validation and security logging
- SecurityManager class integration
- CSRF protection for all forms
- Comprehensive security audit logging

### 2. Settings.php - General System Configuration
**Location:** `church/admin/settings.php` → Multiple Tabs

**Purpose:** General system configuration (non-security settings)

**Settings Managed:**
- **Organization:** Name, description, contact info, social media
- **Appearance:** Colors, themes, logo, custom CSS
- **Email:** SMTP configuration, email templates
- **System:** Timezone, formats, upload limits, backup settings
- **Notifications:** Basic system notifications (NOT security-related)
- **Integrations:** Google Analytics, Facebook Pixel, APIs

**What's NOT Here:**
- Security settings (moved to security_settings.php)
- Password policies, login security, 2FA (in security_settings.php)
- Detailed email template automation (in automated_email_templates.php)

### 3. Automated_email_templates.php - Email Template Management
**Location:** `church/admin/automated_email_templates.php`

**Purpose:** Detailed email template configuration and automation settings

**Settings Managed:**
- **Birthday Emails:** Template selection, send time
- **Birthday Reminders:** Template selection, send time, days before
- **Birthday Notifications:** Template selection, send time, days before (to other members)
- **Welcome Emails:** Template selection, send time

**Features:**
- Multiple template selection for auto-rotation
- Specific send time configuration
- Days before birthday settings
- Template preview functionality
- Direct links to create new templates

## Database Structure

### Settings Table
- General system configuration (non-security)
- Organization info, appearance, email SMTP
- System preferences, integrations
- Basic notifications (non-security related)

### Security_settings Table
- Password policy configuration
- Login security settings (attempts, lockout, session timeout)
- Two-factor authentication settings
- Audit log retention and CSRF settings

### Automated_emails_settings Table
- Email template IDs for each type
- Send times for each email type
- Days before settings for reminders/notifications
- Template rotation configuration

## Navigation

### From Settings Page
- Link to "Security Settings" in the Notifications tab
- Link to "Automated Email Templates" in the Notifications tab
- Clear notes explaining the separation of concerns

### From Security Settings Page
- Link back to "Settings" for general system configuration
- Clear explanation of comprehensive security vs general settings

### From Automated Email Templates Page
- Link back to "Settings → Notifications" for basic notifications
- Link to "Security Settings" for security-related notifications
- Clear explanation of what each page manages

## Benefits of This Organization

1. **Zero Duplication:** Each setting has exactly one location across all three systems
2. **Logical Separation:**
   - General system settings vs comprehensive security vs email automation
   - Basic notifications vs security notifications vs email templates
3. **Professional Structure:** Clear navigation and cross-references between related settings
4. **Enhanced Security:** Dedicated security management with advanced features
5. **Maintainability:** Easier to update and extend each system independently
6. **User Experience:** Clear guidance on where to find specific settings

## Migration Notes

**Removed from Settings.php:**
- All security-related settings (password policies, login security, 2FA, audit logging)
- Birthday notification settings (now managed via template selection)
- Security tab completely removed

**Enhanced in Security_settings.php:**
- Comprehensive password policy management
- Advanced login security with detailed controls
- Professional security audit logging
- Dedicated security_settings database table
- Cross-references to Settings page for general configuration

**Enhanced in Automated_email_templates.php:**
- Clear cross-references to both Settings and Security Settings pages
- Better organization of template types
- Improved user guidance

## Database Cleanup

Run `cleanup_security_settings.php` to:
- Migrate existing security settings from settings table to security_settings table
- Remove duplicate security settings from main settings table
- Ensure database consistency after reorganization

This organization ensures professional separation of concerns with zero duplication across general settings, comprehensive security management, and email template automation.
