<?php
/**
 * Automated Birthday Email System Test Script
 * 
 * This script is designed to be run on a regular schedule (e.g., via cron)
 * to verify that the birthday email system is functioning correctly.
 * 
 * It will:
 * 1. Run a test of the birthday notification system
 * 2. Log the results to a file
 * 3. Send an alert email if issues are detected
 */

// Include necessary files
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/send_birthday_reminders.php';

// Set up logging
$logFile = dirname(__DIR__) . '/logs/birthday_system_tests.log';
if (!file_exists(dirname($logFile))) {
    mkdir(dirname($logFile), 0755, true);
}

// Helper logging function
function testLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    
    // Log to file
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    
    // Also output to standard error for visibility
    error_log($message);
}

// Start the test
testLog("Starting automated birthday system test");

try {
    // Get database connection
    $conn = null;
    if (function_exists('getDbConnection')) {
        $conn = getDbConnection();
        testLog("Successfully created database connection");
    } else {
        // Try to use global PDO from config
        global $pdo;
        $conn = $pdo;
        testLog("Using global PDO connection from config.php");
    }
    
    if (!($conn instanceof PDO)) {
        throw new Exception("Failed to obtain valid database connection");
    }
    
    // Initialize reminder system
    $reminder = new BirthdayReminder($conn);
    testLog("BirthdayReminder system initialized");
    
    // Run the automated test
    $testResults = $reminder->runAutomatedTest('all', 3, 2);
    
    // Log the test results
    $status = $testResults['status'];
    $emailsSent = $testResults['emails_sent'];
    $emailsFailed = $testResults['emails_failed'];
    $dbLogs = $testResults['db_logs_created'];
    
    testLog("Test completed with status: $status");
    testLog("Emails sent: $emailsSent, Failed: $emailsFailed, DB logs: $dbLogs");
    
    // Log all details
    if (!empty($testResults['details'])) {
        foreach ($testResults['details'] as $detail) {
            testLog("Detail: $detail");
        }
    }
    
    // Send alert email if issues detected
    if ($status !== 'success' || $emailsFailed > 0 || ($emailsSent > 0 && $dbLogs == 0)) {
        testLog("Issues detected - sending alert email");
        
        // Format alert email
        $alertSubject = "ALERT: Birthday Email System Test - Status: $status";
        
        $alertBody = "<h2>Birthday Email System Test Results</h2>
        <p><strong>Status:</strong> $status</p>
        <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
        <p><strong>Emails Sent:</strong> $emailsSent</p>
        <p><strong>Emails Failed:</strong> $emailsFailed</p>
        <p><strong>Database Logs:</strong> $dbLogs</p>";
        
        if (isset($testResults['message'])) {
            $alertBody .= "<p><strong>Message:</strong> " . htmlspecialchars($testResults['message']) . "</p>";
        }
        
        if (!empty($testResults['details'])) {
            $alertBody .= "<h3>Details:</h3><ul>";
            foreach ($testResults['details'] as $detail) {
                $alertBody .= "<li>" . htmlspecialchars($detail) . "</li>";
            }
            $alertBody .= "</ul>";
        }
        
        // Get admin email from settings
        $adminEmail = get_site_setting('admin_email', '<EMAIL>');
        
        // Send the alert email
        $emailResult = sendEmail(
            $adminEmail,
            'System Administrator',
            $alertSubject,
            $alertBody,
            true // HTML format
        );
        
        testLog("Alert email " . ($emailResult ? "sent to $adminEmail" : "failed to send"));
    }
    
    // Final output for cron logs
    echo "Birthday system test completed with status: $status\n";
    echo "Emails sent: $emailsSent, Failed: $emailsFailed, DB logs: $dbLogs\n";
    
} catch (Exception $e) {
    $errorMessage = "ERROR: " . $e->getMessage();
    testLog($errorMessage);
    
    // Output for cron logs
    echo "ERROR: Birthday system test failed\n";
    echo $errorMessage . "\n";
    
    // Exit with error code
    exit(1);
}

// Success exit
exit(0); 