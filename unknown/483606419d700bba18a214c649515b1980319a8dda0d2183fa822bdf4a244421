-- User Authentication Schema Enhancement
-- This script adds user authentication capabilities to the existing church management system
-- It modifies the members table and creates supporting tables for user dashboard functionality

-- ============================================================================
-- 1. ENHANCE MEMBERS TABLE FOR USER AUTHENTICATION
-- ============================================================================

-- Add authentication and profile management columns to existing members table
-- Using individual ALTER statements for better compatibility

-- Password authentication columns
ALTER TABLE `members` ADD COLUMN `password_hash` varchar(255) DEFAULT NULL COMMENT 'Hashed password for user authentication';
ALTER TABLE `members` ADD COLUMN `password_reset_token` varchar(255) DEFAULT NULL COMMENT 'Token for password reset functionality';
ALTER TABLE `members` ADD COLUMN `password_reset_expires` datetime DEFAULT NULL COMMENT 'Expiration time for password reset token';
ALTER TABLE `members` ADD COLUMN `password_changed_at` datetime DEFAULT NULL COMMENT 'Timestamp of last password change';
ALTER TABLE `members` ADD COLUMN `temp_password` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Flag indicating if user has temporary password';
ALTER TABLE `members` ADD COLUMN `must_change_password` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Flag requiring password change on next login';

-- Login tracking columns
ALTER TABLE `members` ADD COLUMN `last_login_at` datetime DEFAULT NULL COMMENT 'Timestamp of last successful login';
ALTER TABLE `members` ADD COLUMN `last_login_ip` varchar(45) DEFAULT NULL COMMENT 'IP address of last login';
ALTER TABLE `members` ADD COLUMN `failed_login_attempts` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of failed login attempts';
ALTER TABLE `members` ADD COLUMN `account_locked_until` datetime DEFAULT NULL COMMENT 'Account lockout expiration time';

-- Verification and preferences columns
ALTER TABLE `members` ADD COLUMN `email_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Email verification status';
ALTER TABLE `members` ADD COLUMN `email_verification_token` varchar(255) DEFAULT NULL COMMENT 'Token for email verification';
ALTER TABLE `members` ADD COLUMN `phone_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Phone verification status';
ALTER TABLE `members` ADD COLUMN `profile_visibility` enum('public','members','private') NOT NULL DEFAULT 'members' COMMENT 'Profile visibility setting';
ALTER TABLE `members` ADD COLUMN `notification_preferences` text DEFAULT NULL COMMENT 'JSON encoded notification preferences';
ALTER TABLE `members` ADD COLUMN `timezone` varchar(50) DEFAULT 'UTC' COMMENT 'User timezone preference';
ALTER TABLE `members` ADD COLUMN `language` varchar(10) DEFAULT 'en' COMMENT 'User language preference';
ALTER TABLE `members` ADD COLUMN `dashboard_layout` text DEFAULT NULL COMMENT 'JSON encoded dashboard layout preferences';
ALTER TABLE `members` ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Account active status';
ALTER TABLE `members` ADD COLUMN `image_path` varchar(255) DEFAULT NULL COMMENT 'Profile image file path';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS `idx_members_email` ON `members` (`email`);
CREATE INDEX IF NOT EXISTS `idx_members_phone` ON `members` (`phone_number`);
CREATE INDEX IF NOT EXISTS `idx_members_password_reset` ON `members` (`password_reset_token`);
CREATE INDEX IF NOT EXISTS `idx_members_email_verification` ON `members` (`email_verification_token`);
CREATE INDEX IF NOT EXISTS `idx_members_status` ON `members` (`status`);

-- ============================================================================
-- 2. USER LOGIN ATTEMPTS TRACKING
-- ============================================================================

CREATE TABLE IF NOT EXISTS `user_login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) NOT NULL COMMENT 'Email or phone number used for login',
  `member_id` int(11) DEFAULT NULL COMMENT 'Member ID if login was successful',
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 for successful login, 0 for failed',
  `failure_reason` varchar(100) DEFAULT NULL COMMENT 'Reason for login failure',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_identifier` (`identifier`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 3. USER ACTIVITY LOGS
-- ============================================================================

CREATE TABLE IF NOT EXISTS `user_activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `activity_type` varchar(100) NOT NULL COMMENT 'Type of activity (login, profile_update, event_rsvp, etc.)',
  `activity_description` text DEFAULT NULL COMMENT 'Detailed description of the activity',
  `entity_type` varchar(50) DEFAULT NULL COMMENT 'Type of entity affected (event, profile, etc.)',
  `entity_id` int(11) DEFAULT NULL COMMENT 'ID of the affected entity',
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `metadata` text DEFAULT NULL COMMENT 'JSON encoded additional data',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_activity_type` (`activity_type`),
  KEY `idx_entity` (`entity_type`, `entity_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 4. USER SESSIONS
-- ============================================================================

CREATE TABLE IF NOT EXISTS `user_sessions` (
  `id` varchar(128) NOT NULL COMMENT 'Session ID',
  `member_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `session_data` text DEFAULT NULL COMMENT 'Serialized session data',
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_last_activity` (`last_activity`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 5. EVENTS TABLE (for user event management)
-- ============================================================================

CREATE TABLE IF NOT EXISTS `events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `event_type` varchar(50) NOT NULL DEFAULT 'general' COMMENT 'Type of event (service, meeting, social, etc.)',
  `start_date` datetime NOT NULL,
  `end_date` datetime DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `max_attendees` int(11) DEFAULT NULL COMMENT 'Maximum number of attendees (NULL for unlimited)',
  `requires_rsvp` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether RSVP is required',
  `rsvp_deadline` datetime DEFAULT NULL,
  `created_by` int(11) NOT NULL COMMENT 'Admin ID who created the event',
  `status` enum('draft','published','cancelled','completed') NOT NULL DEFAULT 'draft',
  `visibility` enum('public','members','private') NOT NULL DEFAULT 'members',
  `metadata` text DEFAULT NULL COMMENT 'JSON encoded additional event data',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_status` (`status`),
  KEY `idx_visibility` (`visibility`),
  KEY `idx_created_by` (`created_by`),
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 6. EVENT RSVPS
-- ============================================================================

CREATE TABLE IF NOT EXISTS `event_rsvps` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_id` int(11) NOT NULL,
  `member_id` int(11) NOT NULL,
  `response` enum('yes','no','maybe') NOT NULL DEFAULT 'yes',
  `guests_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of additional guests',
  `notes` text DEFAULT NULL COMMENT 'Additional notes from the member',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_event_member` (`event_id`, `member_id`),
  KEY `idx_event_id` (`event_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_response` (`response`),
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 7. BIRTHDAY TEMPLATES (for user birthday system)
-- ============================================================================

CREATE TABLE IF NOT EXISTS `birthday_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `template_content` text NOT NULL COMMENT 'HTML content of the template',
  `thumbnail_path` varchar(255) DEFAULT NULL COMMENT 'Path to template thumbnail image',
  `category` varchar(50) DEFAULT 'general' COMMENT 'Template category (formal, casual, religious, etc.)',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_user_selectable` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether users can select this template',
  `created_by` int(11) NOT NULL COMMENT 'Admin ID who created the template',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of times template has been used',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_user_selectable` (`is_user_selectable`),
  KEY `idx_created_by` (`created_by`),
  FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 8. USER BIRTHDAY MESSAGES
-- ============================================================================

CREATE TABLE IF NOT EXISTS `user_birthday_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL COMMENT 'Member ID of the sender',
  `recipient_id` int(11) NOT NULL COMMENT 'Member ID of the recipient',
  `template_id` int(11) DEFAULT NULL COMMENT 'Birthday template used',
  `custom_message` text DEFAULT NULL COMMENT 'Custom message added by sender',
  `scheduled_date` date NOT NULL COMMENT 'Date when message should be sent',
  `sent_at` datetime DEFAULT NULL COMMENT 'When the message was actually sent',
  `status` enum('scheduled','sent','failed','cancelled') NOT NULL DEFAULT 'scheduled',
  `delivery_method` enum('email','sms','both') NOT NULL DEFAULT 'email',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_recipient_id` (`recipient_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_scheduled_date` (`scheduled_date`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`sender_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`recipient_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`template_id`) REFERENCES `birthday_templates` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 9. USER PREFERENCES
-- ============================================================================

CREATE TABLE IF NOT EXISTS `user_preferences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL,
  `preference_key` varchar(100) NOT NULL,
  `preference_value` text DEFAULT NULL,
  `preference_type` enum('string','integer','boolean','json') NOT NULL DEFAULT 'string',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_member_preference` (`member_id`, `preference_key`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_preference_key` (`preference_key`),
  FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================================================
-- 10. INSERT DEFAULT DATA
-- ============================================================================

-- DISABLED: Static birthday templates (now using dynamic admin-created templates)
-- The system now uses email_templates table with is_birthday_template = 1
-- instead of static birthday_templates entries

/*
-- Insert default birthday templates
INSERT IGNORE INTO `birthday_templates` (`id`, `name`, `description`, `template_content`, `category`, `created_by`) VALUES
(1, 'Classic Birthday Wishes', 'A traditional birthday message template',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
    <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h2 style="color: #2c3e50; text-align: center; margin-bottom: 20px;">🎉 Happy Birthday! 🎉</h2>
        <p style="font-size: 16px; line-height: 1.6; color: #34495e;">Dear {{recipient_name}},</p>
        <p style="font-size: 16px; line-height: 1.6; color: #34495e;">Wishing you a wonderful birthday filled with joy, laughter, and all your heart desires!</p>
        <p style="font-size: 16px; line-height: 1.6; color: #34495e;">{{custom_message}}</p>
        <p style="font-size: 16px; line-height: 1.6; color: #34495e;">Best wishes,<br>{{sender_name}}</p>
    </div>
</div>', 'formal', 1),

(2, 'Casual Birthday Greeting', 'A friendly and casual birthday message',
'<div style="font-family: Comic Sans MS, cursive; max-width: 600px; margin: 0 auto; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div style="background-color: #ffffff; padding: 25px; border-radius: 15px; text-align: center;">
        <h1 style="color: #e74c3c; font-size: 28px; margin-bottom: 15px;">🎂 HAPPY BIRTHDAY! 🎂</h1>
        <p style="font-size: 18px; color: #2c3e50; margin-bottom: 15px;">Hey {{recipient_name}}!</p>
        <p style="font-size: 16px; color: #34495e; margin-bottom: 15px;">Hope your special day is absolutely amazing!</p>
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <p style="font-size: 16px; color: #495057; font-style: italic;">{{custom_message}}</p>
        </div>
        <p style="font-size: 16px; color: #34495e;">Cheers,<br>{{sender_name}} 🎈</p>
    </div>
</div>', 'casual', 1);
*/

-- ============================================================================
-- 11. CREATE VIEWS FOR EASY DATA ACCESS
-- ============================================================================

-- View for user dashboard statistics
CREATE OR REPLACE VIEW `user_dashboard_stats` AS
SELECT 
    m.id as member_id,
    m.full_name,
    m.email,
    m.phone_number,
    m.last_login_at,
    COUNT(DISTINCT er.id) as events_attended,
    COUNT(DISTINCT ubm.id) as birthday_messages_sent,
    COUNT(DISTINCT ual.id) as total_activities
FROM members m
LEFT JOIN event_rsvps er ON m.id = er.member_id AND er.response = 'yes'
LEFT JOIN user_birthday_messages ubm ON m.id = ubm.sender_id
LEFT JOIN user_activity_logs ual ON m.id = ual.member_id
WHERE m.status = 'active'
GROUP BY m.id, m.full_name, m.email, m.phone_number, m.last_login_at;

-- View for upcoming events
CREATE OR REPLACE VIEW `upcoming_events` AS
SELECT 
    e.*,
    a.full_name as created_by_name,
    COUNT(er.id) as rsvp_count,
    COUNT(CASE WHEN er.response = 'yes' THEN 1 END) as yes_count,
    COUNT(CASE WHEN er.response = 'no' THEN 1 END) as no_count,
    COUNT(CASE WHEN er.response = 'maybe' THEN 1 END) as maybe_count
FROM events e
LEFT JOIN admins a ON e.created_by = a.id
LEFT JOIN event_rsvps er ON e.id = er.event_id
WHERE e.status = 'published' AND e.start_date > NOW()
GROUP BY e.id
ORDER BY e.start_date ASC;

-- ============================================================================
-- SCHEMA ENHANCEMENT COMPLETE
-- ============================================================================
