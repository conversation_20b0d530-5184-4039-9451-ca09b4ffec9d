<?php
require_once 'config.php';

// Simple test to check birthday templates layout
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Templates Layout Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .template-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .template-preview {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            max-height: 200px;
            overflow: hidden;
            position: relative;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .template-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(transparent, #f8f9fa);
        }

        .category-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2><i class="bi bi-gift"></i> Birthday Templates Layout Test</h2>
        
        <?php
        try {
            // Get birthday templates
            $stmt = $pdo->prepare("
                SELECT
                    id,
                    template_name as name,
                    subject as description,
                    content as template_content,
                    template_category as category
                FROM email_templates
                WHERE is_birthday_template = 1
                ORDER BY template_category, template_name
            ");
            $stmt->execute();
            $templates = $stmt->fetchAll();
            
            echo "<p>Found " . count($templates) . " birthday templates</p>";
            
            if (count($templates) == 0) {
                echo "<div class='alert alert-warning'>No birthday templates found in database!</div>";
            } else {
                // Group templates by category
                $templatesByCategory = [];
                foreach ($templates as $template) {
                    $category = $template['category'] ?: 'general';
                    if (!isset($templatesByCategory[$category])) {
                        $templatesByCategory[$category] = [];
                    }
                    $templatesByCategory[$category][] = $template;
                }
                
                foreach ($templatesByCategory as $category => $categoryTemplates) {
                    echo "<div class='mb-5'>";
                    echo "<h4 class='mb-3'>";
                    echo "<span class='category-badge'>" . ucfirst($category) . "</span> Templates (" . count($categoryTemplates) . ")";
                    echo "</h4>";
                    
                    echo "<div class='row'>";
                    foreach ($categoryTemplates as $template) {
                        echo "<div class='col-md-6 col-lg-4 mb-4'>";
                        echo "<div class='template-card'>";
                        echo "<div class='template-preview'>";
                        
                        // Create a sample preview
                        $previewContent = $template['template_content'];
                        $previewContent = str_replace(['{full_name}', '{first_name}'], ['John Doe', 'John'], $previewContent);
                        
                        if (strlen($previewContent) > 400) {
                            $previewContent = substr($previewContent, 0, 400) . '...';
                        }
                        
                        echo $previewContent;
                        echo "</div>";
                        
                        echo "<h6 class='mb-2'>" . htmlspecialchars($template['name']) . "</h6>";
                        
                        if ($template['description']) {
                            echo "<p class='text-muted small mb-3'>" . htmlspecialchars($template['description']) . "</p>";
                        }
                        
                        echo "<div class='mt-auto'>";
                        echo "<button class='btn btn-outline-primary btn-sm me-2'>";
                        echo "<i class='bi bi-eye'></i> Preview";
                        echo "</button>";
                        echo "<button class='btn btn-primary btn-sm'>";
                        echo "<i class='bi bi-envelope'></i> Use Template";
                        echo "</button>";
                        echo "</div>";
                        
                        echo "</div>"; // template-card
                        echo "</div>"; // col
                    }
                    echo "</div>"; // row
                    echo "</div>"; // mb-5
                }
            }
            
        } catch (PDOException $e) {
            echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <hr>
        <p><a href="user/birthday_templates.php" class="btn btn-secondary">← Back to Birthday Templates</a></p>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
