<?php
// Production Readiness Check
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Production Readiness Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .check { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warn { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>";

echo "<h1>Production Readiness Check</h1>";

$checks = [];
$errors = 0;
$warnings = 0;
$passes = 0;

function addCheck($name, $status, $message, $recommendation = '') {
    global $checks, $errors, $warnings, $passes;
    
    $checks[] = [
        'name' => $name,
        'status' => $status,
        'message' => $message,
        'recommendation' => $recommendation
    ];
    
    if ($status === 'FAIL') $errors++;
    elseif ($status === 'WARN') $warnings++;
    else $passes++;
    
    $class = strtolower($status);
    $icon = $status === 'PASS' ? '✅' : ($status === 'WARN' ? '⚠️' : '❌');
    
    echo "<div class='check $class'>$icon <strong>$name:</strong> $message";
    if ($recommendation) {
        echo "<br><em>Recommendation: $recommendation</em>";
    }
    echo "</div>";
}

// 1. Database Configuration Check
echo "<div class='section'><h2>Database Configuration</h2>";

try {
    $stmt = $pdo->query("SELECT 1");
    addCheck("Database Connection", "PASS", "Database connection successful");
} catch (Exception $e) {
    addCheck("Database Connection", "FAIL", "Database connection failed: " . $e->getMessage(), "Check database credentials and server status");
}

// Check for required tables
$requiredTables = ['members', 'email_templates', 'admins', 'settings', 'events', 'donations'];
foreach ($requiredTables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            addCheck("Table: $table", "PASS", "Table exists");
        } else {
            addCheck("Table: $table", "FAIL", "Required table missing", "Run database migration scripts");
        }
    } catch (Exception $e) {
        addCheck("Table: $table", "FAIL", "Error checking table: " . $e->getMessage());
    }
}

echo "</div>";

// 2. File System Check
echo "<div class='section'><h2>File System</h2>";

// Check upload directories
$uploadDirs = ['uploads', 'uploads/profiles', 'uploads/events', 'logs'];
foreach ($uploadDirs as $dir) {
    $fullPath = __DIR__ . '/' . $dir;
    if (is_dir($fullPath)) {
        if (is_writable($fullPath)) {
            addCheck("Directory: $dir", "PASS", "Directory exists and is writable");
        } else {
            addCheck("Directory: $dir", "WARN", "Directory exists but not writable", "Set proper permissions (755 or 775)");
        }
    } else {
        addCheck("Directory: $dir", "FAIL", "Directory does not exist", "Create directory with proper permissions");
    }
}

// Check critical files
$criticalFiles = ['config.php', 'send_birthday_reminders.php', 'admin/includes/header.php'];
foreach ($criticalFiles as $file) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        addCheck("File: $file", "PASS", "Critical file exists");
    } else {
        addCheck("File: $file", "FAIL", "Critical file missing", "Restore file from backup or repository");
    }
}

echo "</div>";

// 3. Configuration Check
echo "<div class='section'><h2>Configuration</h2>";

// Check for hardcoded localhost references
$configContent = file_get_contents(__DIR__ . '/config.php');
if (strpos($configContent, 'localhost') !== false || strpos($configContent, '127.0.0.1') !== false) {
    addCheck("Hardcoded Localhost", "WARN", "Localhost references found in config", "Replace with environment variables or dynamic detection");
} else {
    addCheck("Hardcoded Localhost", "PASS", "No hardcoded localhost references");
}

// Check SMTP configuration
$smtpHost = get_site_setting('smtp_host', '');
if (empty($smtpHost)) {
    addCheck("SMTP Configuration", "WARN", "SMTP not configured", "Configure SMTP settings for email functionality");
} else {
    addCheck("SMTP Configuration", "PASS", "SMTP host configured: $smtpHost");
}

// Check site URL configuration
$siteUrl = defined('SITE_URL') ? SITE_URL : '';
if (empty($siteUrl) || strpos($siteUrl, 'localhost') !== false) {
    addCheck("Site URL", "WARN", "Site URL not properly configured for production", "Set SITE_URL to production domain");
} else {
    addCheck("Site URL", "PASS", "Site URL configured: $siteUrl");
}

echo "</div>";

// 4. Security Check
echo "<div class='section'><h2>Security</h2>";

// Check for exposed sensitive files
$sensitiveFiles = ['phpinfo.php', 'test.php', 'debug.php'];
foreach ($sensitiveFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        addCheck("Sensitive File: $file", "WARN", "Sensitive file exists", "Remove or restrict access in production");
    } else {
        addCheck("Sensitive File: $file", "PASS", "Sensitive file not found");
    }
}

// Check session configuration
if (ini_get('session.cookie_secure') == '1') {
    addCheck("Secure Cookies", "PASS", "Secure cookies enabled");
} else {
    addCheck("Secure Cookies", "WARN", "Secure cookies not enabled", "Enable secure cookies for HTTPS");
}

echo "</div>";

// 5. Email System Check
echo "<div class='section'><h2>Email System</h2>";

try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_templates");
    $templateCount = $stmt->fetch()['count'];
    
    if ($templateCount > 0) {
        addCheck("Email Templates", "PASS", "$templateCount email templates found");
    } else {
        addCheck("Email Templates", "WARN", "No email templates found", "Import default templates");
    }
} catch (Exception $e) {
    addCheck("Email Templates", "FAIL", "Error checking templates: " . $e->getMessage());
}

// Check for broken shortcodes
try {
    $stmt = $pdo->query("SELECT id, template_name, content FROM email_templates LIMIT 5");
    $templates = $stmt->fetchAll();
    
    $brokenShortcodes = 0;
    foreach ($templates as $template) {
        preg_match_all('/{([^}]+)}/', $template['content'], $matches);
        foreach ($matches[1] as $shortcode) {
            if (strpos($shortcode, 'undefined') !== false || strlen($shortcode) > 50) {
                $brokenShortcodes++;
            }
        }
    }
    
    if ($brokenShortcodes === 0) {
        addCheck("Template Shortcodes", "PASS", "No broken shortcodes detected");
    } else {
        addCheck("Template Shortcodes", "WARN", "$brokenShortcodes potentially broken shortcodes found", "Review template shortcodes");
    }
} catch (Exception $e) {
    addCheck("Template Shortcodes", "FAIL", "Error checking shortcodes: " . $e->getMessage());
}

echo "</div>";

// 6. Performance Check
echo "<div class='section'><h2>Performance</h2>";

// Check PHP version
$phpVersion = PHP_VERSION;
if (version_compare($phpVersion, '8.0', '>=')) {
    addCheck("PHP Version", "PASS", "PHP $phpVersion (recommended)");
} elseif (version_compare($phpVersion, '7.4', '>=')) {
    addCheck("PHP Version", "WARN", "PHP $phpVersion (consider upgrading)", "Upgrade to PHP 8.0+ for better performance");
} else {
    addCheck("PHP Version", "FAIL", "PHP $phpVersion (outdated)", "Upgrade to PHP 8.0+ immediately");
}

// Check memory limit
$memoryLimit = ini_get('memory_limit');
$memoryBytes = return_bytes($memoryLimit);
if ($memoryBytes >= 256 * 1024 * 1024) {
    addCheck("Memory Limit", "PASS", "Memory limit: $memoryLimit");
} else {
    addCheck("Memory Limit", "WARN", "Memory limit: $memoryLimit (low)", "Increase to at least 256M");
}

function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }
    return $val;
}

echo "</div>";

// Summary
echo "<div class='section'><h2>Summary</h2>";
echo "<table>";
echo "<tr><th>Status</th><th>Count</th><th>Percentage</th></tr>";
$total = $passes + $warnings + $errors;
echo "<tr><td>✅ Passed</td><td>$passes</td><td>" . round(($passes/$total)*100, 1) . "%</td></tr>";
echo "<tr><td>⚠️ Warnings</td><td>$warnings</td><td>" . round(($warnings/$total)*100, 1) . "%</td></tr>";
echo "<tr><td>❌ Failed</td><td>$errors</td><td>" . round(($errors/$total)*100, 1) . "%</td></tr>";
echo "<tr><td><strong>Total</strong></td><td><strong>$total</strong></td><td><strong>100%</strong></td></tr>";
echo "</table>";

if ($errors === 0 && $warnings <= 2) {
    echo "<div class='check pass'><strong>🎉 Production Ready!</strong> Your application is ready for deployment with minimal issues.</div>";
} elseif ($errors === 0) {
    echo "<div class='check warn'><strong>⚠️ Nearly Ready</strong> Address the warnings before deploying to production.</div>";
} else {
    echo "<div class='check fail'><strong>❌ Not Ready</strong> Critical issues must be resolved before production deployment.</div>";
}

echo "</div>";

echo "<p><em>Check completed at: " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
