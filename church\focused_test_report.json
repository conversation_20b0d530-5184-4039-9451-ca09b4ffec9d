{"summary": {"total": 40, "passed": 35, "failed": 5, "success_rate": 87.5}, "results": [{"test": "Page Access: /index.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:14"}, {"test": "Page Access: /config.php", "status": "FAIL", "message": "Expected 200, got 403", "timestamp": "2025-06-27 21:10:14"}, {"test": "Page Access: /register.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:14"}, {"test": "Page Access: /events.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /event_detail.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /donate.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /admin/login.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /admin/dashboard.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /admin/members.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /admin/add_member.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /admin/email_templates.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:15"}, {"test": "Page Access: /admin/birthday.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:16"}, {"test": "Page Access: /admin/bulk_email.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:16"}, {"test": "Page Access: /admin/settings.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:16"}, {"test": "Page Access: /admin/appearance_settings.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:16"}, {"test": "Page Access: /admin/events.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:16"}, {"test": "Page Access: /admin/donations.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:17"}, {"test": "Page Access: /admin/contacts.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:17"}, {"test": "Page Access: /admin/whatsapp_templates.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:17"}, {"test": "Page Access: /user/login.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:17"}, {"test": "Page Access: /user/register.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:17"}, {"test": "Page Access: /user/dashboard.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /user/profile.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /user/events.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /api/birthdays.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /ajax/get_recipient_details.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /ajax/send_single_email.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /quick_validation.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /comprehensive_template_analysis.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /production_readiness_check.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /comprehensive_system_test.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /test_shortcodes.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /test_birthday_fixes.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /check_database.php", "status": "FAIL", "message": "Expected 200, got 500", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /check_members.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:18"}, {"test": "Page Access: /check_smtp.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:19"}, {"test": "Page Access: /phpinfo.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:10:19"}, {"test": "Page Access: /cron/birthday_reminders.php", "status": "FAIL", "message": "Expected 200, got 403", "timestamp": "2025-06-27 21:10:19"}, {"test": "Page Access: /cron/process_birthday_reminders.php", "status": "FAIL", "message": "Expected 200, got 403", "timestamp": "2025-06-27 21:10:19"}, {"test": "Page Access: /cron/event_reminders.php", "status": "FAIL", "message": "Expected 200, got 403", "timestamp": "2025-06-27 21:10:19"}]}