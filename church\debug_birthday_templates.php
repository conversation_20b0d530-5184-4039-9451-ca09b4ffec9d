<?php
require_once 'config.php';

echo "<h2>Birthday Templates Debug</h2>";

try {
    // Check if email_templates table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ email_templates table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ email_templates table exists</p>";
    
    // Get table structure
    echo "<h3>Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE email_templates");
    $columns = $stmt->fetchAll();
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
    // Get all email templates
    echo "<h3>All Email Templates:</h3>";
    $stmt = $pdo->query("SELECT id, template_name, subject, is_birthday_template, template_category FROM email_templates ORDER BY id");
    $allTemplates = $stmt->fetchAll();
    
    echo "<p>Total templates: " . count($allTemplates) . "</p>";
    
    if (count($allTemplates) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Subject</th><th>Is Birthday</th><th>Category</th></tr>";
        foreach ($allTemplates as $template) {
            $isBirthday = $template['is_birthday_template'] ? 'YES' : 'NO';
            echo "<tr>";
            echo "<td>" . $template['id'] . "</td>";
            echo "<td>" . htmlspecialchars($template['template_name']) . "</td>";
            echo "<td>" . htmlspecialchars($template['subject']) . "</td>";
            echo "<td style='color: " . ($template['is_birthday_template'] ? 'green' : 'red') . ";'>" . $isBirthday . "</td>";
            echo "<td>" . htmlspecialchars($template['template_category'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Get only birthday templates
    echo "<h3>Birthday Templates Only:</h3>";
    $stmt = $pdo->query("SELECT id, template_name, subject, template_category FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_category, template_name");
    $birthdayTemplates = $stmt->fetchAll();
    
    echo "<p>Birthday templates count: " . count($birthdayTemplates) . "</p>";
    
    if (count($birthdayTemplates) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Subject</th><th>Category</th></tr>";
        foreach ($birthdayTemplates as $template) {
            echo "<tr>";
            echo "<td>" . $template['id'] . "</td>";
            echo "<td>" . htmlspecialchars($template['template_name']) . "</td>";
            echo "<td>" . htmlspecialchars($template['subject']) . "</td>";
            echo "<td>" . htmlspecialchars($template['template_category'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No birthday templates found!</p>";
    }
    
    // Test the exact query used in birthday_templates.php
    echo "<h3>Testing Birthday Templates Page Query:</h3>";
    $stmt = $pdo->prepare("
        SELECT
            id,
            template_name as name,
            subject as description,
            content as template_content,
            template_category as category,
            NULL as thumbnail_path,
            COALESCE(
                (SELECT COUNT(*) FROM email_template_usage
                 WHERE template_id = email_templates.id AND template_type = 'birthday'),
                0
            ) as usage_count
        FROM email_templates
        WHERE is_birthday_template = 1
        ORDER BY template_category, template_name
    ");
    $stmt->execute();
    $pageTemplates = $stmt->fetchAll();
    
    echo "<p>Query result count: " . count($pageTemplates) . "</p>";
    
    if (count($pageTemplates) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Description</th><th>Category</th><th>Usage Count</th></tr>";
        foreach ($pageTemplates as $template) {
            echo "<tr>";
            echo "<td>" . $template['id'] . "</td>";
            echo "<td>" . htmlspecialchars($template['name']) . "</td>";
            echo "<td>" . htmlspecialchars($template['description']) . "</td>";
            echo "<td>" . htmlspecialchars($template['category'] ?? 'NULL') . "</td>";
            echo "<td>" . $template['usage_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Group by category like the page does
    echo "<h3>Grouped by Category:</h3>";
    $templatesByCategory = [];
    foreach ($pageTemplates as $template) {
        $category = $template['category'] ?: 'general';
        if (!isset($templatesByCategory[$category])) {
            $templatesByCategory[$category] = [];
        }
        $templatesByCategory[$category][] = $template;
    }
    
    foreach ($templatesByCategory as $category => $categoryTemplates) {
        echo "<h4>Category: " . ucfirst($category) . " (" . count($categoryTemplates) . " templates)</h4>";
        echo "<ul>";
        foreach ($categoryTemplates as $template) {
            echo "<li>" . htmlspecialchars($template['name']) . "</li>";
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<p><a href="user/birthday_templates.php">← Back to Birthday Templates</a></p>
