<?php
/**
 * Fix Image Paths in Database
 * 
 * This script checks and fixes image paths in the database to ensure
 * they follow the correct format for display
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$fixMode = isset($_GET['fix']) && $_GET['fix'] === 'true';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Fix Image Paths</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card'>
                <div class='card-header bg-warning text-dark'>
                    <h5 class='mb-0'><i class='bi bi-tools'></i> Fix Image Paths in Database</h5>
                </div>
                <div class='card-body'>
";

try {
    // Get all members with image paths
    $stmt = $conn->prepare("SELECT id, full_name, image_path FROM members WHERE image_path IS NOT NULL AND image_path != ''");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($members) > 0) {
        echo "<h6>Analyzing " . count($members) . " members with image paths:</h6>";
        
        $needsFix = [];
        $alreadyCorrect = [];
        $missingFiles = [];
        
        foreach ($members as $member) {
            $dbPath = $member['image_path'];
            $uploadDir = __DIR__ . '/../uploads/';
            
            // Check if path is in correct format (uploads/filename.ext)
            if (strpos($dbPath, 'uploads/') === 0) {
                // Path looks correct, check if file exists
                $fullPath = __DIR__ . '/../' . $dbPath;
                if (file_exists($fullPath)) {
                    $alreadyCorrect[] = $member;
                } else {
                    // File missing, check if it exists with just filename
                    $filename = basename($dbPath);
                    $altPath = $uploadDir . $filename;
                    if (file_exists($altPath)) {
                        $needsFix[] = [
                            'member' => $member,
                            'current_path' => $dbPath,
                            'correct_path' => 'uploads/' . $filename,
                            'reason' => 'File exists but path format needs correction'
                        ];
                    } else {
                        $missingFiles[] = $member;
                    }
                }
            } else {
                // Path format is wrong, try to fix it
                $filename = basename($dbPath);
                $correctPath = 'uploads/' . $filename;
                $fullPath = $uploadDir . $filename;
                
                if (file_exists($fullPath)) {
                    $needsFix[] = [
                        'member' => $member,
                        'current_path' => $dbPath,
                        'correct_path' => $correctPath,
                        'reason' => 'Path format incorrect'
                    ];
                } else {
                    $missingFiles[] = $member;
                }
            }
        }
        
        // Display results
        if (count($alreadyCorrect) > 0) {
            echo "<div class='alert alert-success'>";
            echo "<h6>✅ " . count($alreadyCorrect) . " members have correct image paths:</h6>";
            echo "<ul class='mb-0'>";
            foreach (array_slice($alreadyCorrect, 0, 5) as $member) {
                echo "<li>" . htmlspecialchars($member['full_name']) . " - " . htmlspecialchars($member['image_path']) . "</li>";
            }
            if (count($alreadyCorrect) > 5) {
                echo "<li><em>... and " . (count($alreadyCorrect) - 5) . " more</em></li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
        if (count($needsFix) > 0) {
            echo "<div class='alert alert-warning'>";
            echo "<h6>⚠️ " . count($needsFix) . " members need path fixes:</h6>";
            echo "<div class='table-responsive'>";
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>Name</th><th>Current Path</th><th>Correct Path</th><th>Reason</th></tr></thead>";
            echo "<tbody>";
            foreach ($needsFix as $fix) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($fix['member']['full_name']) . "</td>";
                echo "<td><code>" . htmlspecialchars($fix['current_path']) . "</code></td>";
                echo "<td><code>" . htmlspecialchars($fix['correct_path']) . "</code></td>";
                echo "<td>" . htmlspecialchars($fix['reason']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
            echo "</div>";
            
            if (!$fixMode) {
                echo "<div class='alert alert-info'>";
                echo "<p><strong>Ready to fix these paths?</strong></p>";
                echo "<a href='?fix=true' class='btn btn-warning'>Fix All Paths</a>";
                echo "</div>";
            } else {
                // Apply fixes
                echo "<div class='alert alert-info'>";
                echo "<h6>Applying fixes...</h6>";
                echo "<ul>";
                
                $fixed = 0;
                foreach ($needsFix as $fix) {
                    try {
                        $stmt = $conn->prepare("UPDATE members SET image_path = ? WHERE id = ?");
                        $stmt->execute([$fix['correct_path'], $fix['member']['id']]);
                        echo "<li>✅ Fixed: " . htmlspecialchars($fix['member']['full_name']) . " - " . htmlspecialchars($fix['current_path']) . " → " . htmlspecialchars($fix['correct_path']) . "</li>";
                        $fixed++;
                    } catch (PDOException $e) {
                        echo "<li>❌ Error fixing " . htmlspecialchars($fix['member']['full_name']) . ": " . htmlspecialchars($e->getMessage()) . "</li>";
                    }
                }
                
                echo "</ul>";
                echo "<div class='alert alert-success mt-3'>";
                echo "<h6>🎉 Fixed $fixed image paths!</h6>";
                echo "<p class='mb-0'>The members page should now display user images correctly.</p>";
                echo "<a href='members.php' class='btn btn-primary mt-2'>Test Members Page</a>";
                echo "</div>";
                echo "</div>";
            }
        }
        
        if (count($missingFiles) > 0) {
            echo "<div class='alert alert-danger'>";
            echo "<h6>❌ " . count($missingFiles) . " members have missing image files:</h6>";
            echo "<ul class='mb-0'>";
            foreach (array_slice($missingFiles, 0, 5) as $member) {
                echo "<li>" . htmlspecialchars($member['full_name']) . " - " . htmlspecialchars($member['image_path']) . "</li>";
            }
            if (count($missingFiles) > 5) {
                echo "<li><em>... and " . (count($missingFiles) - 5) . " more</em></li>";
            }
            echo "</ul>";
            echo "<p class='mt-2 mb-0'><small>These members will show default profile images.</small></p>";
            echo "</div>";
        }
        
        // Summary
        echo "<hr>";
        echo "<div class='row text-center'>";
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body'>";
        echo "<h4>" . count($alreadyCorrect) . "</h4>";
        echo "<p class='mb-0'>Correct</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-warning text-dark'>";
        echo "<div class='card-body'>";
        echo "<h4>" . count($needsFix) . "</h4>";
        echo "<p class='mb-0'>Need Fix</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body'>";
        echo "<h4>" . count($missingFiles) . "</h4>";
        echo "<p class='mb-0'>Missing</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-info text-white'>";
        echo "<div class='card-body'>";
        echo "<h4>" . count($members) . "</h4>";
        echo "<p class='mb-0'>Total</p>";
        echo "</div></div></div>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-info'>No members with image paths found in database</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Database Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='members.php' class='btn btn-primary'>Test Members Page</a>
                        <a href='test_image_paths.php' class='btn btn-outline-info'>Test Image Paths</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
