<?php
// Test file upload functionality
echo "<h2>File Upload Test</h2>";

// Check if uploads directory exists and is writable
$upload_dir = 'uploads/';
echo "<h3>Directory Check:</h3>";
echo "Upload directory exists: " . (is_dir($upload_dir) ? "✅ YES" : "❌ NO") . "<br>";
echo "Upload directory writable: " . (is_writable($upload_dir) ? "✅ YES" : "❌ NO") . "<br>";
echo "Upload directory path: " . realpath($upload_dir) . "<br>";

// Check PHP upload settings
echo "<h3>PHP Upload Settings:</h3>";
echo "file_uploads: " . (ini_get('file_uploads') ? "✅ Enabled" : "❌ Disabled") . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Upload Test Results:</h3>";
    
    if (isset($_FILES['test_file'])) {
        $file = $_FILES['test_file'];
        
        echo "File name: " . htmlspecialchars($file['name']) . "<br>";
        echo "File type: " . htmlspecialchars($file['type']) . "<br>";
        echo "File size: " . $file['size'] . " bytes<br>";
        echo "File error: " . $file['error'] . " (" . 
             ($file['error'] === UPLOAD_ERR_OK ? "No error" : 
              ($file['error'] === UPLOAD_ERR_INI_SIZE ? "File too large (ini_size)" :
               ($file['error'] === UPLOAD_ERR_FORM_SIZE ? "File too large (form_size)" :
                ($file['error'] === UPLOAD_ERR_PARTIAL ? "Partial upload" :
                 ($file['error'] === UPLOAD_ERR_NO_FILE ? "No file uploaded" :
                  ($file['error'] === UPLOAD_ERR_NO_TMP_DIR ? "No temp directory" :
                   ($file['error'] === UPLOAD_ERR_CANT_WRITE ? "Can't write to disk" :
                    ($file['error'] === UPLOAD_ERR_EXTENSION ? "Extension stopped upload" : "Unknown error")))))))) . ")<br>";
        echo "Temp file: " . htmlspecialchars($file['tmp_name']) . "<br>";
        echo "Temp file exists: " . (file_exists($file['tmp_name']) ? "✅ YES" : "❌ NO") . "<br>";
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            $target_file = $upload_dir . 'test_' . time() . '_' . basename($file['name']);
            if (move_uploaded_file($file['tmp_name'], $target_file)) {
                echo "<strong>✅ Upload successful!</strong> File saved to: " . $target_file . "<br>";
                if (file_exists($target_file)) {
                    echo "File size on disk: " . filesize($target_file) . " bytes<br>";
                }
            } else {
                echo "<strong>❌ Upload failed!</strong> Could not move file to destination.<br>";
            }
        }
    } else {
        echo "❌ No file data received in \$_FILES array<br>";
    }
    
    echo "<h4>Raw \$_FILES data:</h4>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    echo "<h4>Raw \$_POST data:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-form { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="test-form">
        <h3>Test File Upload</h3>
        <form method="POST" enctype="multipart/form-data">
            <label for="test_file">Select a test image file:</label><br>
            <input type="file" name="test_file" id="test_file" accept="image/*" required><br><br>
            <button type="submit">Test Upload</button>
        </form>
    </div>
    
    <p><a href="register.php">← Back to Registration</a></p>
</body>
</html>
