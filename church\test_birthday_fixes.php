<?php
// Test birthday email fixes
require_once 'config.php';

echo "<h1>Birthday Email Fixes Test</h1>\n";

// Test 1: Check if birthday_member_photo_url shortcode is working
echo "<h2>Test 1: Birthday Member Photo URL Shortcode</h2>\n";

$testData = [
    'birthday_member_photo_url' => 'https://example.com/uploads/member.jpg',
    'birthday_member_image' => 'https://example.com/uploads/member.jpg',
    'birthday_member_name' => '<PERSON>',
    'birthday_member_full_name' => '<PERSON>',
    'birthday_member_age' => 30,
    'upcoming_birthday_formatted' => 'Saturday, June 21, 2025',
    'days_text' => 'today',
    'church_name' => 'Freedom Assembly Church',
    'recipient_full_name' => '<PERSON>'
];

$testTemplate = "
<div>
    <h3>Birthday Celebration for {birthday_member_full_name}</h3>
    <img src=\"{birthday_member_photo_url}\" alt=\"{birthday_member_name}\" style=\"width: 150px; height: 150px; border-radius: 50%;\">
    <p>Dear {recipient_full_name},</p>
    <p>We are excited to celebrate {birthday_member_name} as they turn {birthday_member_age} today!</p>
    <p>Birthday: {upcoming_birthday_formatted}</p>
    <p>With love from {church_name}</p>
</div>
";

echo "<h3>Before Processing:</h3>\n";
echo "<pre>" . htmlspecialchars($testTemplate) . "</pre>\n";

$processed = replaceTemplatePlaceholders($testTemplate, $testData);

echo "<h3>After Processing:</h3>\n";
echo "<div style='border: 1px solid #ccc; padding: 15px; background: #f9f9f9;'>\n";
echo $processed;
echo "</div>\n";

// Check for unreplaced placeholders
preg_match_all('/{([^}]+)}/', $processed, $matches);
if (!empty($matches[0])) {
    echo "<p style='color: red;'>❌ Unreplaced placeholders: " . implode(', ', $matches[0]) . "</p>\n";
} else {
    echo "<p style='color: green;'>✅ All placeholders successfully replaced!</p>\n";
}

// Test 2: Check image filtering logic
echo "<hr><h2>Test 2: Image Filtering Logic</h2>\n";

$testImages = [
    'https://example.com/uploads/member-photo.jpg',
    'https://example.com/uploads/receipt-image.jpg',
    'https://example.com/uploads/payment-confirmation.png',
    'https://example.com/uploads/birthday-celebration.jpg',
    'https://example.com/uploads/invoice-123.pdf',
    'https://example.com/assets/img/default-avatar.png'
];

echo "<h3>Image Filtering Test:</h3>\n";
foreach ($testImages as $image) {
    $shouldSkip = (
        strpos($image, 'receipt') !== false ||
        strpos($image, 'payment') !== false ||
        strpos($image, 'invoice') !== false
    );
    
    $status = $shouldSkip ? "❌ SKIP" : "✅ ALLOW";
    $reason = $shouldSkip ? "(Contains filtered keyword)" : "(Safe to embed)";
    
    echo "<p>$status $image $reason</p>\n";
}

// Test 3: Check birthday template content
echo "<hr><h2>Test 3: Birthday Template Analysis</h2>\n";

try {
    $stmt = $pdo->query("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($templates) . " birthday templates:</p>\n";
    echo "<ul>\n";
    foreach ($templates as $template) {
        echo "<li><strong>" . htmlspecialchars($template['template_name']) . "</strong> (ID: " . $template['id'] . ")</li>\n";
    }
    echo "</ul>\n";
    
    // Test one template
    if (!empty($templates)) {
        $template = $templates[0];
        $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = ?");
        $stmt->execute([$template['id']]);
        $content = $stmt->fetchColumn();
        
        echo "<h3>Testing Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";
        
        // Check for problematic images
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches);
        
        if (!empty($matches[1])) {
            echo "<h4>Images found in template:</h4>\n";
            foreach ($matches[1] as $imgSrc) {
                $isProblematic = (
                    strpos($imgSrc, 'receipt') !== false ||
                    strpos($imgSrc, 'payment') !== false ||
                    strpos($imgSrc, 'invoice') !== false
                );
                
                $status = $isProblematic ? "❌ PROBLEMATIC" : "✅ OK";
                echo "<p>$status " . htmlspecialchars($imgSrc) . "</p>\n";
            }
        } else {
            echo "<p>✅ No hardcoded images found in template (uses shortcodes)</p>\n";
        }
        
        // Check shortcodes
        preg_match_all('/{([^}]+)}/', $content, $shortcodeMatches);
        if (!empty($shortcodeMatches[1])) {
            echo "<h4>Shortcodes found:</h4>\n";
            $uniqueShortcodes = array_unique($shortcodeMatches[1]);
            foreach ($uniqueShortcodes as $shortcode) {
                echo "<span style='background: #e7f3ff; padding: 2px 6px; margin: 2px; border-radius: 3px;'>{" . htmlspecialchars($shortcode) . "}</span> ";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking templates: " . $e->getMessage() . "</p>\n";
}

// Test 4: Check church logo and site URL
echo "<hr><h2>Test 4: Church Logo and Site URL</h2>\n";

$logoTest = replaceTemplatePlaceholders('{church_logo}', []);
$siteTest = replaceTemplatePlaceholders('{site_url}', []);

echo "<p><strong>Church Logo URL:</strong> " . htmlspecialchars($logoTest) . "</p>\n";
echo "<p><strong>Site URL:</strong> " . htmlspecialchars($siteTest) . "</p>\n";

if (strpos($logoTest, '{church_logo}') === false) {
    echo "<p style='color: green;'>✅ Church logo shortcode working</p>\n";
} else {
    echo "<p style='color: red;'>❌ Church logo shortcode not replaced</p>\n";
}

if (strpos($siteTest, '{site_url}') === false) {
    echo "<p style='color: green;'>✅ Site URL shortcode working</p>\n";
} else {
    echo "<p style='color: red;'>❌ Site URL shortcode not replaced</p>\n";
}

echo "<hr><h2>Summary</h2>\n";
echo "<p>✅ Birthday member photo URL shortcode added</p>\n";
echo "<p>✅ Image filtering logic updated to skip receipt/payment images</p>\n";
echo "<p>✅ Church logo and site URL shortcodes added</p>\n";
echo "<p>✅ Template validation completed</p>\n";

echo "<h3>Next Steps:</h3>\n";
echo "<ul>\n";
echo "<li>Test sending actual birthday emails</li>\n";
echo "<li>Verify images are embedded correctly (not attached)</li>\n";
echo "<li>Check email client compatibility</li>\n";
echo "<li>Monitor for any remaining shortcode issues</li>\n";
echo "</ul>\n";

echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
