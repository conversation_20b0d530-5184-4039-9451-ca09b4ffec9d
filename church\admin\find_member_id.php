<?php
require_once '../config.php';

try {
    // Get all members with their IDs
    $stmt = $conn->prepare("SELECT id, full_name, image_path FROM members ORDER BY full_name");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>All Members with IDs:</h2>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Full Name</th><th>Image Path</th><th>Debug Link</th></tr>";
    
    foreach ($members as $member) {
        echo "<tr>";
        echo "<td>" . $member['id'] . "</td>";
        echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($member['image_path'] ?? 'NULL') . "</td>";
        echo "<td><a href='debug_member_image.php?id=" . $member['id'] . "' target='_blank'>Debug</a></td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (PDOException $e) {
    echo "Database error: " . htmlspecialchars($e->getMessage());
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
th { background-color: #f5f5f5; }
</style>
