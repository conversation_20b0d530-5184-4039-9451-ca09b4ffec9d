<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Current email settings
$emailSettings = [
    'smtp_host' => 'smtp.hostinger.com',
    'smtp_auth' => true,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => '!3wlI!dL',
    'smtp_secure' => 'ssl',
    'smtp_port' => 465,
    'sender_email' => '<EMAIL>',
    'sender_name' => 'Freedom Assembly Church International',
    'admin_email' => '<EMAIL>',
    'reply_to_email' => '<EMAIL>'
];

// Load settings from database
try {
    $stmt = $pdo->prepare("SELECT * FROM email_settings");
    $stmt->execute();
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $key = $row['setting_key'];
        $emailSettings[$key] = $row['setting_value'];
    }
    
    // Also get admin_email from settings table
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'admin_email'");
    $stmt->execute();
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $emailSettings['admin_email'] = $row['setting_value'];
    }
} catch (PDOException $e) {
    // If table doesn't exist, create it
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS email_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // Insert default settings
        $defaultSettings = [
            'smtp_host' => 'smtp.hostinger.com',
            'smtp_auth' => '1',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => '!3wlI!dL',
            'smtp_secure' => 'ssl',
            'smtp_port' => '465',
            'sender_email' => '<EMAIL>',
            'sender_name' => 'Freedom Assembly Church International'
        ];
        
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?)");
        
        foreach ($defaultSettings as $key => $value) {
            $stmt->execute([$key, $value]);
        }
        
        // Make sure admin_email is in the settings table
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) 
                               VALUES ('admin_email', '<EMAIL>')
                               ON DUPLICATE KEY UPDATE setting_value = setting_value");
        $stmt->execute();
        
    } catch (PDOException $e2) {
        $error = "Could not create email_settings table: " . $e2->getMessage();
    }
}

// Handle settings update
if (isset($_POST['update_settings'])) {
    try {
        // Validate SMTP settings
        $smtp_host = trim($_POST['smtp_host']);
        $smtp_port = intval($_POST['smtp_port']);
        $smtp_username = trim($_POST['smtp_username']);
        $smtp_password = trim($_POST['smtp_password']);
        $sender_email = trim($_POST['sender_email']);
        $sender_name = trim($_POST['sender_name']);
        $admin_email = trim($_POST['admin_email']);
        $reply_to_email = trim($_POST['reply_to_email']);
        $smtp_secure = $_POST['smtp_secure'];
        $smtp_auth = isset($_POST['smtp_auth']) ? '1' : '0';
        
        // Basic validation
        if (empty($smtp_host) || empty($smtp_username) || empty($sender_email) || empty($sender_name)) {
            throw new Exception("SMTP Host, Username, Sender Email and Sender Name are required.");
        }
        
        if (!filter_var($sender_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Sender Email is not a valid email address.");
        }
        
        if (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Admin Email is not a valid email address.");
        }
        
        if (!filter_var($reply_to_email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Reply-To Email is not a valid email address.");
        }
        
        if ($smtp_port <= 0 || $smtp_port > 65535) {
            throw new Exception("SMTP Port must be a valid port number.");
        }
        
        // Update settings in database - BOTH prefixed and non-prefixed versions
        // 1. Settings with 'email_' prefix (used by the application)
        $prefixedSettings = [
            'email_smtp_host' => $smtp_host,
            'email_smtp_auth' => $smtp_auth,
            'email_smtp_username' => $smtp_username,
            'email_smtp_password' => $smtp_password,
            'email_smtp_secure' => $smtp_secure,
            'email_smtp_port' => $smtp_port,
            'email_sender_email' => $sender_email,
            'email_sender_name' => $sender_name,
            'email_reply_to_email' => $reply_to_email
        ];
        
        // 2. Settings without prefix (for backward compatibility)
        $nonPrefixedSettings = [
            'smtp_host' => $smtp_host,
            'smtp_auth' => $smtp_auth,
            'smtp_username' => $smtp_username,
            'smtp_password' => $smtp_password,
            'smtp_secure' => $smtp_secure,
            'smtp_port' => $smtp_port,
            'sender_email' => $sender_email,
            'sender_name' => $sender_name,
            'reply_to_email' => $reply_to_email
        ];
        
        // 3. Admin email (no prefix)
        $adminSettings = [
            'admin_email' => $admin_email
        ];
        
        // Update all settings
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?)
                             ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        
        // Update email settings in our new table
        foreach ($nonPrefixedSettings as $key => $value) {
            $stmt->execute([$key, $value]);
        }
        
        // Update admin_email in the settings table
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)
                             ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        foreach ($adminSettings as $key => $value) {
            $stmt->execute([$key, $value]);
        }
        
        // For backward compatibility, also keep the prefixed versions in settings table
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)
                             ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        foreach ($prefixedSettings as $key => $value) {
            $stmt->execute([$key, $value]);
        }
        
        // Update current settings
        $emailSettings = [
            'smtp_host' => $smtp_host,
            'smtp_auth' => $smtp_auth == '1',
            'smtp_username' => $smtp_username,
            'smtp_password' => $smtp_password,
            'smtp_secure' => $smtp_secure,
            'smtp_port' => $smtp_port,
            'sender_email' => $sender_email,
            'sender_name' => $sender_name,
            'admin_email' => $admin_email,
            'reply_to_email' => $reply_to_email
        ];
        
        $message = "Email settings updated successfully.";
    } catch (Exception $e) {
        $error = "Failed to update settings: " . $e->getMessage();
    }
}

// Handle test email
if (isset($_POST['test_email'])) {
    $test_email = $_POST['test_email'];
    $test_name = $_POST['test_name'] ?? 'Church Admin';
    
    $test_subject = 'Freedom Assembly Church - Email Test';
    $test_body = '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333;">Email Configuration Test</h2>
        <p style="line-height: 1.6; color: #555;">This is a test email from Freedom Assembly Church International admin panel. If you are receiving this email, it means your email configuration is working correctly.</p>
        <p style="line-height: 1.6; color: #555;">You can now send birthday messages and other emails to church members.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
            This email was sent from Freedom Assembly Church International Admin Panel.
        </div>
    </div>';
    
    global $last_email_error;
    if (sendEmail($test_email, $test_name, $test_subject, $test_body)) {
        $message = "Test email sent successfully to $test_email";
    } else {
        $error = "Failed to send test email. Error details: " . ($last_email_error ?? "Unknown error");
    }
}

// Close connection
$conn = null;

// Set page variables
$page_title = 'Email Settings';
$page_header = 'Email Settings';
$page_description = 'Configure your email settings for sending messages to church members.';

// Include header
include 'includes/header.php';

// Display success message if it exists
if (!empty($message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display error message if it exists
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Email Settings Configuration -->
<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-gear-fill me-1"></i>
                SMTP Configuration
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <!-- SMTP Server Settings -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="smtp_host" class="form-label">SMTP Host</label>
                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($emailSettings['smtp_host']); ?>" required>
                            <div class="form-text">e.g., smtp.gmail.com, smtp.hostinger.com</div>
                        </div>
                        <div class="col-md-3">
                            <label for="smtp_port" class="form-label">SMTP Port</label>
                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($emailSettings['smtp_port']); ?>" required>
                            <div class="form-text">Common ports: 587, 465, 25</div>
                        </div>
                        <div class="col-md-3">
                            <label for="smtp_secure" class="form-label">Encryption</label>
                            <select class="form-select" id="smtp_secure" name="smtp_secure">
                                <option value="tls" <?php echo $emailSettings['smtp_secure'] == 'tls' ? 'selected' : ''; ?>>TLS</option>
                                <option value="ssl" <?php echo $emailSettings['smtp_secure'] == 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                <option value="none" <?php echo $emailSettings['smtp_secure'] == 'none' ? 'selected' : ''; ?>>None</option>
                            </select>
                            <div class="form-text">TLS: Port 587, SSL: Port 465</div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="smtp_auth" name="smtp_auth" <?php echo $emailSettings['smtp_auth'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="smtp_auth">
                                    SMTP Authentication Required
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3" id="auth_fields">
                        <div class="col-md-6">
                            <label for="smtp_username" class="form-label">SMTP Username</label>
                            <input type="text" class="form-control" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($emailSettings['smtp_username']); ?>">
                            <div class="form-text">Usually your email address</div>
                        </div>
                        <div class="col-md-6">
                            <label for="smtp_password" class="form-label">SMTP Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($emailSettings['smtp_password']); ?>">
                                <button class="btn btn-outline-secondary" type="button" id="toggle_password">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Your email password or app password</div>
                        </div>
                    </div>
                    
                    <!-- Sender Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="sender_email" class="form-label">Sender Email</label>
                            <input type="email" class="form-control" id="sender_email" name="sender_email" value="<?php echo htmlspecialchars($emailSettings['sender_email']); ?>" required>
                            <div class="form-text">The email address that appears in the "From" field</div>
                        </div>
                        <div class="col-md-6">
                            <label for="sender_name" class="form-label">Sender Name</label>
                            <input type="text" class="form-control" id="sender_name" name="sender_name" value="<?php echo htmlspecialchars($emailSettings['sender_name']); ?>" required>
                            <div class="form-text">The name that appears in the "From" field</div>
                        </div>
                    </div>
                    
                    <!-- Reply-To Email -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="reply_to_email" class="form-label">Reply-To Email</label>
                            <input type="email" class="form-control" id="reply_to_email" name="reply_to_email" value="<?php echo htmlspecialchars($emailSettings['reply_to_email']); ?>" required>
                            <div class="form-text">Email address that members will reply to when they respond to church emails</div>
                        </div>
                    </div>
                    
                    <!-- Admin Email -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="admin_email" class="form-label">Admin Email</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($emailSettings['admin_email']); ?>" required>
                            <div class="form-text">Email address to receive system notifications</div>
                        </div>
                    </div>
                    
                    <button type="submit" name="update_settings" class="btn btn-primary">
                        <i class="bi bi-save me-1"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Email Test Section -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-envelope-check me-1"></i>
                Test Email Configuration
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <div class="mb-3">
                        <label for="test_email" class="form-label">Recipient Email</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="test_name" class="form-label">Recipient Name</label>
                        <input type="text" class="form-control" id="test_name" name="test_name" value="Church Admin">
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-send me-1"></i> Send Test Email
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Configuration Help -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-question-circle me-1"></i>
                Help & Information
            </div>
            <div class="card-body">
                <h5>Common SMTP Settings</h5>
                <div class="accordion" id="smtpHelp">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingGmail">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGmail" aria-expanded="false" aria-controls="collapseGmail">
                                Gmail
                            </button>
                        </h2>
                        <div id="collapseGmail" class="accordion-collapse collapse" aria-labelledby="headingGmail" data-bs-parent="#smtpHelp">
                            <div class="accordion-body">
                                <ul class="list-unstyled">
                                    <li><strong>SMTP Host:</strong> smtp.gmail.com</li>
                                    <li><strong>SMTP Port:</strong> 587</li>
                                    <li><strong>Encryption:</strong> TLS</li>
                                    <li><strong>Username:</strong> Your full Gmail address</li>
                                    <li><strong>Password:</strong> Your app password (not your Gmail password)</li>
                                </ul>
                                <div class="alert alert-info small">
                                    <i class="bi bi-info-circle me-1"></i> For Gmail, you need to set up an app password. Go to your Google Account → Security → App passwords
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingHostinger">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHostinger" aria-expanded="false" aria-controls="collapseHostinger">
                                Hostinger
                            </button>
                        </h2>
                        <div id="collapseHostinger" class="accordion-collapse collapse" aria-labelledby="headingHostinger" data-bs-parent="#smtpHelp">
                            <div class="accordion-body">
                                <ul class="list-unstyled">
                                    <li><strong>SMTP Host:</strong> smtp.hostinger.com</li>
                                    <li><strong>SMTP Port:</strong> 465</li>
                                    <li><strong>Encryption:</strong> SSL</li>
                                    <li><strong>Username:</strong> Your email address</li>
                                    <li><strong>Password:</strong> Your email password</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOffice365">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOffice365" aria-expanded="false" aria-controls="collapseOffice365">
                                Office 365
                            </button>
                        </h2>
                        <div id="collapseOffice365" class="accordion-collapse collapse" aria-labelledby="headingOffice365" data-bs-parent="#smtpHelp">
                            <div class="accordion-body">
                                <ul class="list-unstyled">
                                    <li><strong>SMTP Host:</strong> smtp.office365.com</li>
                                    <li><strong>SMTP Port:</strong> 587</li>
                                    <li><strong>Encryption:</strong> TLS</li>
                                    <li><strong>Username:</strong> Your Office 365 email</li>
                                    <li><strong>Password:</strong> Your Office 365 password</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3 small">
                    <i class="bi bi-exclamation-triangle me-1"></i> Always test your email configuration after making changes to ensure emails are being sent correctly.
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('toggle_password');
    const password = document.getElementById('smtp_password');
    
    togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        
        // Toggle eye icon
        this.querySelector('i').classList.toggle('bi-eye');
        this.querySelector('i').classList.toggle('bi-eye-slash');
    });
    
    // Toggle authentication fields visibility
    const smtpAuth = document.getElementById('smtp_auth');
    const authFields = document.getElementById('auth_fields');
    
    function toggleAuthFields() {
        if (smtpAuth.checked) {
            authFields.style.display = 'flex';
        } else {
            authFields.style.display = 'none';
        }
    }
    
    smtpAuth.addEventListener('change', toggleAuthFields);
    toggleAuthFields(); // Initial state
});
</script>

<?php include 'includes/footer.php'; ?> 