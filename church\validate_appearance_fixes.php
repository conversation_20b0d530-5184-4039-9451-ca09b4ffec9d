<?php
// Simple Validation of Appearance Fixes
require_once 'config.php';

echo "<h1>Appearance and Theming Fixes Validation</h1>";

$results = [];

// Test 1: Check if CSS generation function exists
echo "<h2>1. CSS Generation and Cascade ✅</h2>";
$appearanceFile = __DIR__ . '/admin/appearance_settings.php';
if (file_exists($appearanceFile)) {
    $content = file_get_contents($appearanceFile);
    if (strpos($content, 'generateCustomCSS') !== false) {
        echo "<p style='color: green;'>✅ CSS generation function exists</p>";
        $results['css_generation'] = true;
    } else {
        echo "<p style='color: red;'>❌ CSS generation function missing</p>";
        $results['css_generation'] = false;
    }
} else {
    echo "<p style='color: red;'>❌ Appearance settings file not found</p>";
    $results['css_generation'] = false;
}

// Test 2: Check header includes favicon and custom CSS
echo "<h2>2. Header Template Updates ✅</h2>";
$headerFile = __DIR__ . '/admin/includes/header.php';
if (file_exists($headerFile)) {
    $content = file_get_contents($headerFile);
    
    $faviconSupport = strpos($content, 'favicon') !== false;
    $customCssSupport = strpos($content, 'custom-theme.css') !== false;
    
    if ($faviconSupport) {
        echo "<p style='color: green;'>✅ Favicon support added to header</p>";
    } else {
        echo "<p style='color: red;'>❌ Favicon support missing</p>";
    }
    
    if ($customCssSupport) {
        echo "<p style='color: green;'>✅ Custom theme CSS inclusion added</p>";
    } else {
        echo "<p style='color: red;'>❌ Custom theme CSS inclusion missing</p>";
    }
    
    $results['header_updates'] = $faviconSupport && $customCssSupport;
} else {
    echo "<p style='color: red;'>❌ Header file not found</p>";
    $results['header_updates'] = false;
}

// Test 3: Check sidebar logo implementation
echo "<h2>3. Sidebar Logo Implementation ✅</h2>";
$sidebarFile = __DIR__ . '/admin/includes/sidebar.php';
if (file_exists($sidebarFile)) {
    $content = file_get_contents($sidebarFile);
    
    $logoContainer = strpos($content, 'logo-container') !== false;
    $logoSettings = strpos($content, 'get_site_setting(\'logo_url\'') !== false;
    $logoHeight = strpos($content, 'logo_height') !== false;
    
    if ($logoContainer) {
        echo "<p style='color: green;'>✅ Logo container implemented</p>";
    } else {
        echo "<p style='color: red;'>❌ Logo container missing</p>";
    }
    
    if ($logoSettings) {
        echo "<p style='color: green;'>✅ Logo settings integration added</p>";
    } else {
        echo "<p style='color: red;'>❌ Logo settings integration missing</p>";
    }
    
    if ($logoHeight) {
        echo "<p style='color: green;'>✅ Logo height customization added</p>";
    } else {
        echo "<p style='color: red;'>❌ Logo height customization missing</p>";
    }
    
    $results['sidebar_logo'] = $logoContainer && $logoSettings && $logoHeight;
} else {
    echo "<p style='color: red;'>❌ Sidebar file not found</p>";
    $results['sidebar_logo'] = false;
}

// Test 4: Check CSS variables and spacing
echo "<h2>4. CSS Variables and Spacing ✅</h2>";
$cssFile = __DIR__ . '/admin/css/admin-style.css';
if (file_exists($cssFile)) {
    $content = file_get_contents($cssFile);
    
    $sidebarVars = strpos($content, 'var(--sidebar-bg-color') !== false;
    $spacingCalc = strpos($content, 'calc(var(--sidebar-width') !== false;
    $logoStyling = strpos($content, '.sidebar-logo') !== false;
    
    if ($sidebarVars) {
        echo "<p style='color: green;'>✅ Sidebar CSS variables implemented</p>";
    } else {
        echo "<p style='color: red;'>❌ Sidebar CSS variables missing</p>";
    }
    
    if ($spacingCalc) {
        echo "<p style='color: green;'>✅ Dynamic spacing calculation added</p>";
    } else {
        echo "<p style='color: red;'>❌ Dynamic spacing calculation missing</p>";
        echo "<p style='color: orange;'>Debug: Looking for 'calc(var(--sidebar-width)' in CSS file</p>";
        // Show a snippet of what we found
        $lines = explode("\n", $content);
        foreach ($lines as $i => $line) {
            if (strpos($line, 'calc(') !== false) {
                echo "<p style='color: blue;'>Found calc on line " . ($i+1) . ": " . htmlspecialchars(trim($line)) . "</p>";
            }
        }
    }
    
    if ($logoStyling) {
        echo "<p style='color: green;'>✅ Logo CSS styling added</p>";
    } else {
        echo "<p style='color: red;'>❌ Logo CSS styling missing</p>";
    }
    
    $results['css_variables'] = $sidebarVars && $spacingCalc && $logoStyling;
} else {
    echo "<p style='color: red;'>❌ Admin CSS file not found</p>";
    $results['css_variables'] = false;
}

// Test 5: Check form fields
echo "<h2>5. Appearance Settings Form Fields ✅</h2>";
if (file_exists($appearanceFile)) {
    $content = file_get_contents($appearanceFile);
    
    $sidebarColors = strpos($content, 'sidebar_bg_color') !== false && 
                     strpos($content, 'sidebar_text_color') !== false &&
                     strpos($content, 'sidebar_hover_color') !== false;
    
    $logoFields = strpos($content, 'logo_url') !== false &&
                  strpos($content, 'favicon_url') !== false &&
                  strpos($content, 'logo_height') !== false;
    
    $spacingField = strpos($content, 'content_spacing') !== false;
    
    if ($sidebarColors) {
        echo "<p style='color: green;'>✅ Sidebar color fields added</p>";
    } else {
        echo "<p style='color: red;'>❌ Sidebar color fields missing</p>";
    }
    
    if ($logoFields) {
        echo "<p style='color: green;'>✅ Logo and favicon fields added</p>";
    } else {
        echo "<p style='color: red;'>❌ Logo and favicon fields missing</p>";
    }
    
    if ($spacingField) {
        echo "<p style='color: green;'>✅ Content spacing field added</p>";
    } else {
        echo "<p style='color: red;'>❌ Content spacing field missing</p>";
    }
    
    $results['form_fields'] = $sidebarColors && $logoFields && $spacingField;
} else {
    $results['form_fields'] = false;
}

// Summary
echo "<hr><h2>Summary</h2>";
$totalTests = count($results);
$passedTests = count(array_filter($results));
$successRate = ($passedTests / $totalTests) * 100;

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Test Category</th><th>Status</th></tr>";
foreach ($results as $test => $passed) {
    $status = $passed ? "<span style='color: green;'>✅ PASS</span>" : "<span style='color: red;'>❌ FAIL</span>";
    echo "<tr><td>" . ucwords(str_replace('_', ' ', $test)) . "</td><td>$status</td></tr>";
}
echo "</table>";

echo "<h3>Overall Results:</h3>";
echo "<p><strong>Tests Passed:</strong> $passedTests / $totalTests</p>";
echo "<p><strong>Success Rate:</strong> " . round($successRate, 1) . "%</p>";

if ($successRate >= 90) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Excellent! All appearance and theming fixes are implemented correctly.</h3>";
    echo "<p>The system now supports:</p>";
    echo "<ul>";
    echo "<li>✅ Global theme cascade across all pages</li>";
    echo "<li>✅ Sidebar theme customization</li>";
    echo "<li>✅ Proper spacing between sidebar and content</li>";
    echo "<li>✅ Logo display in sidebar</li>";
    echo "<li>✅ Favicon support</li>";
    echo "</ul>";
    echo "</div>";
} elseif ($successRate >= 75) {
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>⚠️ Good progress! Most fixes are implemented with minor issues.</h3>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Several issues need to be addressed.</h3>";
    echo "</div>";
}

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Visit the <a href='admin/appearance_settings.php'>Appearance Settings</a> page</li>";
echo "<li>Test changing colors and verify they apply to other admin pages</li>";
echo "<li>Upload a logo URL and test the logo display</li>";
echo "<li>Add a favicon URL and check browser tabs</li>";
echo "<li>Adjust spacing settings and verify layout changes</li>";
echo "</ol>";

echo "<p><em>Validation completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
