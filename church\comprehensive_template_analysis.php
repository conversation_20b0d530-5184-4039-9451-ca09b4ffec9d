<?php
// Comprehensive Email Template Analysis
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Template Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .template { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }
        .error { color: red; }
        .warning { color: orange; }
        .success { color: green; }
        .shortcode { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
        .image-list { background: #f9f9f9; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>";

echo "<h1>Comprehensive Email Template Analysis</h1>";

try {
    // Get all email templates
    $stmt = $pdo->query("
        SELECT id, template_name, subject, content, is_birthday_template, 
               template_category, created_at, updated_at 
        FROM email_templates 
        ORDER BY template_category, template_name
    ");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Summary</h2>";
    echo "<p>Total templates found: <strong>" . count($templates) . "</strong></p>";
    
    // Categorize templates
    $categories = [];
    foreach ($templates as $template) {
        $category = $template['template_category'] ?: 'uncategorized';
        if (!isset($categories[$category])) {
            $categories[$category] = [];
        }
        $categories[$category][] = $template;
    }
    
    echo "<h3>Templates by Category:</h3>";
    echo "<ul>";
    foreach ($categories as $category => $templateList) {
        echo "<li><strong>" . ucfirst($category) . ":</strong> " . count($templateList) . " templates</li>";
    }
    echo "</ul>";
    
    // Analysis results
    $totalIssues = 0;
    $templateIssues = [];
    
    echo "<h2>Detailed Template Analysis</h2>";
    
    foreach ($categories as $category => $templateList) {
        echo "<h3>Category: " . ucfirst($category) . "</h3>";
        
        foreach ($templateList as $template) {
            echo "<div class='template'>";
            echo "<h4>Template: " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</h4>";
            echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
            echo "<p><strong>Birthday Template:</strong> " . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Last Updated:</strong> " . $template['updated_at'] . "</p>";
            
            $issues = [];
            
            // Check for shortcodes in subject
            preg_match_all('/{([^}]+)}/', $template['subject'], $subjectShortcodes);
            if (!empty($subjectShortcodes[1])) {
                echo "<p><strong>Subject Shortcodes:</strong> ";
                foreach ($subjectShortcodes[1] as $shortcode) {
                    echo "<span class='shortcode'>{" . htmlspecialchars($shortcode) . "}</span> ";
                }
                echo "</p>";
            }
            
            // Check for shortcodes in content
            preg_match_all('/{([^}]+)}/', $template['content'], $contentShortcodes);
            if (!empty($contentShortcodes[1])) {
                $uniqueShortcodes = array_unique($contentShortcodes[1]);
                echo "<p><strong>Content Shortcodes (" . count($uniqueShortcodes) . "):</strong><br>";
                foreach ($uniqueShortcodes as $shortcode) {
                    echo "<span class='shortcode'>{" . htmlspecialchars($shortcode) . "}</span> ";
                }
                echo "</p>";
                
                // Check for potentially problematic shortcodes
                $problematicShortcodes = [];
                foreach ($uniqueShortcodes as $shortcode) {
                    if (strpos($shortcode, 'undefined') !== false || 
                        strpos($shortcode, 'null') !== false ||
                        strlen($shortcode) > 50) {
                        $problematicShortcodes[] = $shortcode;
                    }
                }
                
                if (!empty($problematicShortcodes)) {
                    echo "<p class='error'>⚠️ Potentially problematic shortcodes: ";
                    foreach ($problematicShortcodes as $shortcode) {
                        echo "<span class='shortcode'>{" . htmlspecialchars($shortcode) . "}</span> ";
                    }
                    echo "</p>";
                    $issues[] = "Problematic shortcodes found";
                }
            }
            
            // Check for images in content
            preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $imageMatches);
            if (!empty($imageMatches[1])) {
                echo "<div class='image-list'>";
                echo "<p><strong>Images found (" . count($imageMatches[1]) . "):</strong></p>";
                foreach ($imageMatches[1] as $imgSrc) {
                    $isProblematic = (
                        strpos($imgSrc, 'receipt') !== false ||
                        strpos($imgSrc, 'payment') !== false ||
                        strpos($imgSrc, 'invoice') !== false
                    );
                    
                    $isHardcoded = (
                        strpos($imgSrc, 'http://') === 0 ||
                        strpos($imgSrc, 'https://') === 0
                    ) && strpos($imgSrc, '{') === false;
                    
                    $status = '';
                    if ($isProblematic) {
                        $status = "<span class='error'>❌ PROBLEMATIC</span>";
                        $issues[] = "Problematic image: " . $imgSrc;
                    } elseif ($isHardcoded) {
                        $status = "<span class='warning'>⚠️ HARDCODED</span>";
                        $issues[] = "Hardcoded image: " . $imgSrc;
                    } else {
                        $status = "<span class='success'>✅ OK</span>";
                    }
                    
                    echo "<p>$status " . htmlspecialchars($imgSrc) . "</p>";
                }
                echo "</div>";
            }
            
            // Check for hardcoded URLs
            preg_match_all('/https?:\/\/[^\s<>"\']+/i', $template['content'], $urlMatches);
            if (!empty($urlMatches[0])) {
                $hardcodedUrls = [];
                foreach ($urlMatches[0] as $url) {
                    if (strpos($url, 'localhost') !== false || 
                        strpos($url, '127.0.0.1') !== false ||
                        strpos($url, 'freedomassemblydb.online') !== false) {
                        $hardcodedUrls[] = $url;
                    }
                }
                
                if (!empty($hardcodedUrls)) {
                    echo "<p class='warning'>⚠️ Hardcoded URLs found:</p>";
                    foreach ($hardcodedUrls as $url) {
                        echo "<p style='margin-left: 20px;'>" . htmlspecialchars($url) . "</p>";
                    }
                    $issues[] = "Hardcoded URLs found";
                }
            }
            
            // Check content length and structure
            $contentLength = strlen($template['content']);
            if ($contentLength > 50000) {
                echo "<p class='warning'>⚠️ Large template size: " . number_format($contentLength) . " characters</p>";
                $issues[] = "Large template size";
            }
            
            // Check for HTML structure
            if (stripos($template['content'], '<!DOCTYPE') === false && 
                stripos($template['content'], '<html') === false &&
                $contentLength > 1000) {
                echo "<p class='warning'>⚠️ Missing HTML document structure</p>";
                $issues[] = "Missing HTML structure";
            }
            
            // Summary for this template
            if (empty($issues)) {
                echo "<p class='success'>✅ No issues found</p>";
            } else {
                echo "<p class='error'>❌ Issues found: " . count($issues) . "</p>";
                $templateIssues[$template['id']] = [
                    'name' => $template['template_name'],
                    'issues' => $issues
                ];
                $totalIssues += count($issues);
            }
            
            echo "</div>";
        }
    }
    
    // Overall summary
    echo "<hr><h2>Overall Analysis Summary</h2>";
    echo "<table>";
    echo "<tr><th>Metric</th><th>Value</th></tr>";
    echo "<tr><td>Total Templates</td><td>" . count($templates) . "</td></tr>";
    echo "<tr><td>Templates with Issues</td><td>" . count($templateIssues) . "</td></tr>";
    echo "<tr><td>Total Issues Found</td><td>" . $totalIssues . "</td></tr>";
    echo "<tr><td>Categories</td><td>" . count($categories) . "</td></tr>";
    echo "</table>";
    
    if (!empty($templateIssues)) {
        echo "<h3>Templates Requiring Attention:</h3>";
        echo "<ul>";
        foreach ($templateIssues as $templateId => $templateData) {
            echo "<li><strong>" . htmlspecialchars($templateData['name']) . " (ID: $templateId)</strong>";
            echo "<ul>";
            foreach ($templateData['issues'] as $issue) {
                echo "<li>" . htmlspecialchars($issue) . "</li>";
            }
            echo "</ul></li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
