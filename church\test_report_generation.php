<?php
// Test report generation directly
require_once 'config.php';

// Simulate the report generation
$type = 'summary';
$date_from = date('Y-m-01');
$date_to = date('Y-m-t');
$event_id = null;

echo "<h1>Test Report Generation</h1>";

try {
    // Build query based on report type
    $where_conditions = [];
    $params = [];
    
    if ($date_from) {
        $where_conditions[] = "e.event_date >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "e.event_date <= ?";
        $params[] = $date_to . ' 23:59:59';
    }
    
    if ($event_id) {
        $where_conditions[] = "e.id = ?";
        $params[] = $event_id;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    if ($type === 'attendance') {
        // Attendance report
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees,
                   COUNT(er.id) as total_rsvps,
                   SUM(CASE WHEN er.status = 'attending' THEN 1 ELSE 0 END) as attending_count,
                   SUM(CASE WHEN er.status = 'not_attending' THEN 1 ELSE 0 END) as not_attending_count,
                   SUM(CASE WHEN er.status = 'maybe' THEN 1 ELSE 0 END) as maybe_count
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            $where_clause
            GROUP BY e.id
            ORDER BY e.event_date DESC
        ";
    } else {
        // Event summary report
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees, e.is_active,
                   COUNT(er.id) as total_rsvps
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            $where_clause
            GROUP BY e.id
            ORDER BY e.event_date DESC
        ";
    }
    
    echo "<h2>SQL Query:</h2>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    
    echo "<h2>Parameters:</h2>";
    echo "<pre>";
    print_r($params);
    echo "</pre>";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Query Results:</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    
    echo "<h2>Organization Name Test:</h2>";
    echo "<p>Organization Name: " . htmlspecialchars(get_organization_name()) . "</p>";
    
    echo "<h2>Report Generation Test:</h2>";
    
    // Test the report generation without headers
    $title = ucfirst($type) . " Report";
    $date_range = "";
    if ($date_from || $date_to) {
        $date_range = " (" . ($date_from ?: 'Start') . " to " . ($date_to ?: 'End') . ")";
    }
    
    echo "<p>Title: " . htmlspecialchars($title . $date_range) . "</p>";
    echo "<p>Data Count: " . count($data) . "</p>";
    
    if (!empty($data)) {
        echo "<h3>Sample Data:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr>";
        foreach (array_keys($data[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        echo "</tr>";
        
        foreach (array_slice($data, 0, 3) as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p style='color: green;'>✅ Report generation test successful!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
