<?php
// Debug upload functionality
echo "<h1>Upload Debug Page</h1>";

// Check PHP configuration
echo "<h2>PHP Configuration</h2>";
echo "file_uploads: " . (ini_get('file_uploads') ? "✅ Enabled" : "❌ Disabled") . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";
echo "max_execution_time: " . ini_get('max_execution_time') . "<br>";

// Check upload directory
echo "<h2>Upload Directory</h2>";
$upload_dir = 'uploads/';
echo "Directory exists: " . (is_dir($upload_dir) ? "✅ YES" : "❌ NO") . "<br>";
echo "Directory writable: " . (is_writable($upload_dir) ? "✅ YES" : "❌ NO") . "<br>";
echo "Directory path: " . realpath($upload_dir) . "<br>";

if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    echo "Files in directory: " . (count($files) - 2) . "<br>"; // -2 for . and ..
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Form Submission Debug</h2>";
    
    echo "<h3>POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    if (isset($_FILES['test_upload'])) {
        $file = $_FILES['test_upload'];
        
        echo "<h3>File Analysis:</h3>";
        echo "Original name: " . htmlspecialchars($file['name']) . "<br>";
        echo "MIME type: " . htmlspecialchars($file['type']) . "<br>";
        echo "File size: " . $file['size'] . " bytes (" . round($file['size'] / 1024 / 1024, 2) . " MB)<br>";
        echo "Error code: " . $file['error'] . "<br>";
        echo "Temp file: " . htmlspecialchars($file['tmp_name']) . "<br>";
        echo "Temp file exists: " . (file_exists($file['tmp_name']) ? "✅ YES" : "❌ NO") . "<br>";
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            // Try to move the file
            $target_file = $upload_dir . 'debug_' . time() . '_' . basename($file['name']);
            
            if (move_uploaded_file($file['tmp_name'], $target_file)) {
                echo "<strong style='color: green;'>✅ Upload successful!</strong><br>";
                echo "File saved to: " . $target_file . "<br>";
                echo "File size on disk: " . filesize($target_file) . " bytes<br>";
                
                // Show image if it's an image
                if (strpos($file['type'], 'image/') === 0) {
                    echo "<br><img src='" . $target_file . "' style='max-width: 200px; border: 1px solid #ccc;'><br>";
                }
            } else {
                echo "<strong style='color: red;'>❌ Upload failed!</strong><br>";
                echo "Could not move file from temp location to destination.<br>";
            }
        } else {
            echo "<strong style='color: red;'>❌ Upload error!</strong><br>";
            switch ($file['error']) {
                case UPLOAD_ERR_INI_SIZE:
                    echo "File exceeds upload_max_filesize directive in php.ini<br>";
                    break;
                case UPLOAD_ERR_FORM_SIZE:
                    echo "File exceeds MAX_FILE_SIZE directive in HTML form<br>";
                    break;
                case UPLOAD_ERR_PARTIAL:
                    echo "File was only partially uploaded<br>";
                    break;
                case UPLOAD_ERR_NO_FILE:
                    echo "No file was uploaded<br>";
                    break;
                case UPLOAD_ERR_NO_TMP_DIR:
                    echo "Missing temporary folder<br>";
                    break;
                case UPLOAD_ERR_CANT_WRITE:
                    echo "Failed to write file to disk<br>";
                    break;
                case UPLOAD_ERR_EXTENSION:
                    echo "A PHP extension stopped the file upload<br>";
                    break;
                default:
                    echo "Unknown upload error<br>";
                    break;
            }
        }
    } else {
        echo "<strong style='color: red;'>❌ No file data received in \$_FILES</strong><br>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Upload Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="form-section">
        <h3>Test File Upload</h3>
        <form method="POST" enctype="multipart/form-data">
            <label for="test_upload">Select a file to test upload:</label><br><br>
            <input type="file" name="test_upload" id="test_upload" accept="image/*" required><br><br>
            <button type="submit" class="btn">Test Upload</button>
        </form>
    </div>
    
    <div class="form-section">
        <h3>Browser Information</h3>
        <script>
            document.write('<p><strong>User Agent:</strong> ' + navigator.userAgent + '</p>');
            document.write('<p><strong>File API Support:</strong> ' + (window.File ? 'Yes' : 'No') + '</p>');
            document.write('<p><strong>FileReader Support:</strong> ' + (window.FileReader ? 'Yes' : 'No') + '</p>');
            document.write('<p><strong>FormData Support:</strong> ' + (window.FormData ? 'Yes' : 'No') + '</p>');
        </script>
    </div>
    
    <p><a href="register.php">← Back to Registration</a></p>
    <p><a href="test_registration_form.php">→ Test Registration Form</a></p>
</body>
</html>
