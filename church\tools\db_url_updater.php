<?php
/**
 * Database URL Updater Tool
 * 
 * This script updates hardcoded URLs in database content to use environment variables.
 * It targets email templates and other content stored in the database.
 */

// Include configuration file
require_once dirname(__DIR__) . '/config.php';

// Configuration
$dryRun = true; // Set to false to actually make changes
$backupTable = true; // Create backup of tables before updating

// Process command line arguments
foreach ($argv as $arg) {
    if ($arg === '--update') {
        $dryRun = false;
    }
    if ($arg === '--no-backup') {
        $backupTable = false;
    }
}

// URL mappings to apply - these should match the actual URLs, not regex
$urlMappings = [
    'https://freedomassemblydb.online/church/assets/images/banner.jpg' => '{{CHURCH_LOGO}}',
    'https://freedomassemblydb.online/church/' => '{{SITE_URL}}/',
    'https://freedomassemblydb.online' => '{{SITE_URL}}',
    'http://freedomassemblydb.online/church/' => '{{SITE_URL}}/',
    'http://freedomassemblydb.online' => '{{SITE_URL}}',
    '<EMAIL>' => '{{CHURCH_EMAIL}}',
    '<EMAIL>' => '{{ADMIN_EMAIL}}',
    'https://freedomassemblychurch.org/wp-content/uploads/2023/10/church-logo.png' => '{{CHURCH_LOGO}}'
];

// Email template replacement function
function replaceUrls($content, $urlMappings) {
    foreach ($urlMappings as $url => $replacement) {
        $content = str_replace($url, $replacement, $content);
    }
    return $content;
}

// Tables and columns to update
$tableConfig = [
    'email_templates' => [
        'columns' => ['content', 'subject'],
        'pk' => 'id'
    ],
    'settings' => [
        'columns' => ['setting_value'],
        'pk' => 'id',
        'where' => "setting_key LIKE '%email%' OR setting_key LIKE '%url%'"
    ]
];

// Function to backup a table
function backupTable($pdo, $tableName) {
    $backupName = $tableName . '_backup_' . date('Ymd_His');
    
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS $backupName LIKE $tableName");
        $pdo->exec("INSERT INTO $backupName SELECT * FROM $tableName");
        echo "[INFO] Created backup of table $tableName as $backupName\n";
        return true;
    } catch (PDOException $e) {
        echo "[ERROR] Failed to create backup of table $tableName: " . $e->getMessage() . "\n";
        return false;
    }
}

// Main update function
function updateTableContent($pdo, $tableName, $columns, $pkColumn, $whereClause = null, $urlMappings, $dryRun = true) {
    $totalUpdated = 0;
    
    try {
        // Build query to fetch rows
        $query = "SELECT $pkColumn, " . implode(', ', $columns) . " FROM $tableName";
        if ($whereClause) {
            $query .= " WHERE $whereClause";
        }
        
        $stmt = $pdo->query($query);
        
        // Process each row
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $updates = [];
            $pk = $row[$pkColumn];
            $rowUpdated = false;
            
            foreach ($columns as $column) {
                if (isset($row[$column]) && !empty($row[$column])) {
                    $originalContent = $row[$column];
                    $updatedContent = replaceUrls($originalContent, $urlMappings);
                    
                    if ($originalContent !== $updatedContent) {
                        $updates[] = "$column = :$column" . $pk;
                        $rowUpdated = true;
                    }
                }
            }
            
            if ($rowUpdated) {
                // Build and execute update query
                $updateQuery = "UPDATE $tableName SET " . implode(', ', $updates) . " WHERE $pkColumn = :pk";
                
                if (!$dryRun) {
                    $updateStmt = $pdo->prepare($updateQuery);
                    $updateStmt->bindParam(':pk', $pk);
                    
                    foreach ($columns as $column) {
                        if (isset($row[$column])) {
                            $updatedContent = replaceUrls($row[$column], $urlMappings);
                            $paramName = ':' . $column . $pk;
                            $updateStmt->bindParam($paramName, $updatedContent);
                        }
                    }
                    
                    $updateStmt->execute();
                }
                
                echo "[INFO] Updated row in $tableName with $pkColumn = $pk\n";
                $totalUpdated++;
            }
        }
        
        return $totalUpdated;
    } catch (PDOException $e) {
        echo "[ERROR] Failed to update table $tableName: " . $e->getMessage() . "\n";
        return 0;
    }
}

// Start the update process
echo "[INFO] Starting database URL update process" . ($dryRun ? " (DRY RUN)" : "") . "\n";

// Create backups if requested
if ($backupTable && !$dryRun) {
    foreach ($tableConfig as $tableName => $config) {
        $backupSuccess = backupTable($pdo, $tableName);
        if (!$backupSuccess) {
            echo "[ERROR] Aborting update for $tableName due to backup failure\n";
            unset($tableConfig[$tableName]);
        }
    }
}

$totalRowsUpdated = 0;

// Process each table
foreach ($tableConfig as $tableName => $config) {
    echo "[INFO] Processing table: $tableName\n";
    $whereClause = isset($config['where']) ? $config['where'] : null;
    $rowsUpdated = updateTableContent(
        $pdo, 
        $tableName, 
        $config['columns'], 
        $config['pk'], 
        $whereClause, 
        $urlMappings, 
        $dryRun
    );
    $totalRowsUpdated += $rowsUpdated;
    echo "[INFO] Updated $rowsUpdated rows in $tableName\n";
}

echo "[INFO] Database URL update completed. Total rows updated: $totalRowsUpdated\n";

if ($dryRun) {
    echo "[INFO] This was a dry run. Use --update flag to actually update database content.\n";
}

// Now, create a function to process the environment variables when displaying content
if (!$dryRun) {
    // Check if the function already exists
    $checkFunctionQuery = "SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = DATABASE() AND ROUTINE_NAME = 'process_environment_variables'";
    $functionExists = $pdo->query($checkFunctionQuery)->fetchColumn() > 0;
    
    if (!$functionExists) {
        $createFunctionSql = "
        CREATE FUNCTION process_environment_variables(content TEXT) 
        RETURNS TEXT
        DETERMINISTIC
        BEGIN
            DECLARE site_url VARCHAR(255);
            DECLARE admin_url VARCHAR(255);
            DECLARE assets_url VARCHAR(255);
            DECLARE uploads_url VARCHAR(255);
            DECLARE images_url VARCHAR(255);
            DECLARE church_logo VARCHAR(255);
            DECLARE church_email VARCHAR(255);
            DECLARE admin_email VARCHAR(255);
            
            -- Get values from settings table
            SELECT setting_value INTO site_url FROM settings WHERE setting_key = 'site_url' LIMIT 1;
            SELECT CONCAT(site_url, '/admin') INTO admin_url;
            SELECT CONCAT(site_url, '/assets') INTO assets_url;
            SELECT CONCAT(site_url, '/uploads') INTO uploads_url;
            SELECT CONCAT(assets_url, '/images') INTO images_url;
            SELECT CONCAT(images_url, '/banner.jpg') INTO church_logo;
            SELECT setting_value INTO church_email FROM settings WHERE setting_key = 'email_sender_email' LIMIT 1;
            SELECT setting_value INTO admin_email FROM settings WHERE setting_key = 'admin_email' LIMIT 1;
            
            -- Set defaults if not found
            IF site_url IS NULL THEN SET site_url = 'https://freedomassemblydb.online/church'; END IF;
            IF church_logo IS NULL THEN SET church_logo = CONCAT(site_url, '/assets/images/banner.jpg'); END IF;
            IF church_email IS NULL THEN SET church_email = '<EMAIL>'; END IF;
            IF admin_email IS NULL THEN SET admin_email = '<EMAIL>'; END IF;
            
            -- Replace all placeholders
            SET content = REPLACE(content, '{{SITE_URL}}', site_url);
            SET content = REPLACE(content, '{{ADMIN_URL}}', admin_url);
            SET content = REPLACE(content, '{{ASSETS_URL}}', assets_url);
            SET content = REPLACE(content, '{{UPLOADS_URL}}', uploads_url);
            SET content = REPLACE(content, '{{IMAGES_URL}}', images_url);
            SET content = REPLACE(content, '{{CHURCH_LOGO}}', church_logo);
            SET content = REPLACE(content, '{{CHURCH_EMAIL}}', church_email);
            SET content = REPLACE(content, '{{ADMIN_EMAIL}}', admin_email);
            
            RETURN content;
        END
        ";
        
        try {
            $pdo->exec("DROP FUNCTION IF EXISTS process_environment_variables");
            $pdo->exec($createFunctionSql);
            echo "[INFO] Created MySQL function 'process_environment_variables' for dynamic URL processing\n";
        } catch (PDOException $e) {
            echo "[ERROR] Failed to create MySQL function: " . $e->getMessage() . "\n";
        }
    } else {
        echo "[INFO] MySQL function 'process_environment_variables' already exists\n";
    }
} 