<?php
// Simple registration processing for original church management system
require_once 'config.php';

// Initialize error logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Function to handle file upload
function handleFileUpload($file) {
    error_log('handleFileUpload called with: ' . print_r($file, true));

    if ($file['error'] !== UPLOAD_ERR_OK) {
        error_log('Upload error code: ' . $file['error']);
        return null;
    }

    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowed_types)) {
        error_log('Invalid file type: ' . $file['type']);
        return null;
    }

    if ($file['size'] > $max_size) {
        error_log('File too large: ' . $file['size'] . ' bytes');
        return null;
    }

    $upload_dir = 'uploads/';
    if (!is_dir($upload_dir)) {
        error_log('Creating upload directory: ' . $upload_dir);
        if (!mkdir($upload_dir, 0755, true)) {
            error_log('Failed to create upload directory');
            return null;
        }
    }

    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;

    error_log('Attempting to move file from ' . $file['tmp_name'] . ' to ' . $upload_path);

    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        error_log('File upload successful: ' . $upload_path);
        return $upload_path;
    } else {
        error_log('move_uploaded_file failed');
        return null;
    }
}

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $required_fields = ['full_name', 'email', 'birth_date', 'password', 'confirm_password'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Required field '$field' is missing.");
            }
        }

        // Validate email format
        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format.');
        }

        // Validate password match
        if ($_POST['password'] !== $_POST['confirm_password']) {
            throw new Exception('Passwords do not match.');
        }

        // Get form data
        $full_name = trim($_POST['full_name']);
        $occupation = trim($_POST['occupation'] ?? '');
        $email = trim($_POST['email']);
        $phone_number = trim($_POST['phone_number'] ?? '');
        $home_address = trim($_POST['home_address'] ?? '');
        $birth_date = $_POST['birth_date'];
        $message = trim($_POST['message'] ?? '');
        $password = $_POST['password'];

        // Hash the password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        // Split full name into first and last name
        $name_parts = explode(' ', $full_name, 2);
        $first_name = $name_parts[0];
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

        // Handle file upload
        $image_path = null;
        if (isset($_FILES['profile_image'])) {
            error_log('Profile image upload attempt: ' . print_r($_FILES['profile_image'], true));

            if ($_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                $image_path = handleFileUpload($_FILES['profile_image']);
                if ($image_path) {
                    error_log('File upload successful: ' . $image_path);
                } else {
                    error_log('File upload failed in handleFileUpload function');
                    throw new Exception('Failed to upload profile image. Please try again.');
                }
            } else {
                error_log('File upload error code: ' . $_FILES['profile_image']['error']);
                throw new Exception('File upload error. Please check your image file and try again.');
            }
        } else {
            error_log('No profile_image in $_FILES array');
            throw new Exception('Profile image is required. Please select an image file.');
        }

        // Insert into database including password_hash (if column exists)
        $stmt = $pdo->prepare("
            INSERT INTO members
            (full_name, first_name, last_name, occupation, image_path, email,
             birth_date, home_address, phone_number, message, password_hash, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
        ");

        $stmt->execute([
            $full_name,
            $first_name,
            $last_name,
            $occupation,
            $image_path,
            $email,
            $birth_date,
            $home_address,
            $phone_number,
            $message,
            $password_hash
        ]);

        // Send welcome email using the proper function
        $memberData = [
            'full_name' => $full_name,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'email' => $email,
            'phone_number' => $phone_number,
            'home_address' => $home_address,
            'occupation' => $occupation,
            'image_path' => $image_path
        ];

        try {
            $emailSent = sendWelcomeEmail($memberData);
            if (!$emailSent) {
                error_log('Failed to send welcome email to: ' . $email);
            }
        } catch (Exception $e) {
            error_log('Email sending error: ' . $e->getMessage());
            $emailSent = false;
        }

        // Redirect with success message
        header('Location: register.php?success=1&email=' . ($emailSent ? '1' : '0'));
        exit;

    } catch (PDOException $e) {
        if ($e->getCode() == 23000) { // Duplicate entry error
            header('Location: register.php?error=duplicate_email');
        } else {
            error_log('Database error: ' . $e->getMessage());
            header('Location: register.php?error=db_error');
        }
        exit;
    } catch (Exception $e) {
        error_log('Registration error: ' . $e->getMessage());
        header('Location: register.php?error=general');
        exit;
    }
} else {
    // Not a POST request
    header('Location: register.php');
    exit;
}
?>