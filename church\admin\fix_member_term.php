<?php
/**
 * Fix Member Term Setting
 * 
 * This script fixes the issue where member_term setting is empty,
 * causing the sidebar to show "s" instead of "Members"
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Fix Member Term Setting</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h5 class='mb-0'><i class='bi bi-tools'></i> Fix Member Term Setting</h5>
                </div>
                <div class='card-body'>
";

try {
    // Check current member_term setting
    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'member_term'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<div class='alert alert-info'>Checking member_term setting...</div>";
    
    if (!$result) {
        echo "<div class='alert alert-warning'>member_term setting not found in database.</div>";
        
        // Insert member_term setting
        $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES ('member_term', 'Member')");
        $stmt->execute();
        
        echo "<div class='alert alert-success'>✓ Created member_term setting with value 'Member'.</div>";
    } elseif (empty(trim($result['setting_value']))) {
        echo "<div class='alert alert-warning'>member_term setting exists but is empty: '" . htmlspecialchars($result['setting_value']) . "'</div>";
        
        // Update member_term setting
        $stmt = $conn->prepare("UPDATE settings SET setting_value = 'Member' WHERE setting_key = 'member_term'");
        $stmt->execute();
        
        echo "<div class='alert alert-success'>✓ Updated member_term setting to 'Member'.</div>";
    } else {
        echo "<div class='alert alert-success'>member_term setting is already properly set to: '" . htmlspecialchars($result['setting_value']) . "'</div>";
    }
    
    // Test the functions
    echo "<div class='alert alert-info mt-4'>Testing terminology functions:</div>";
    echo "<ul class='list-group'>";
    echo "<li class='list-group-item'>get_member_term(): <strong>" . htmlspecialchars(get_member_term()) . "</strong></li>";
    echo "<li class='list-group-item'>get_member_term(true): <strong>" . htmlspecialchars(get_member_term(true)) . "</strong></li>";
    echo "</ul>";
    
    // Check other terminology settings
    echo "<div class='alert alert-info mt-4'>Checking other terminology settings:</div>";
    $terminologySettings = [
        'leader_term' => 'Pastor',
        'group_term' => 'Ministry', 
        'event_term' => 'Service',
        'donation_term' => 'Offering'
    ];
    
    $fixed = 0;
    foreach ($terminologySettings as $key => $defaultValue) {
        $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result || empty(trim($result['setting_value']))) {
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                                  ON DUPLICATE KEY UPDATE setting_value = ?");
            $stmt->execute([$key, $defaultValue, $defaultValue]);
            echo "<div class='alert alert-warning'>Fixed empty $key setting - set to '$defaultValue'</div>";
            $fixed++;
        }
    }
    
    if ($fixed === 0) {
        echo "<div class='alert alert-success'>All terminology settings are properly configured.</div>";
    } else {
        echo "<div class='alert alert-info'>Fixed $fixed terminology settings.</div>";
    }
    
    echo "<div class='alert alert-success mt-4'>
        <h6>✅ Member Term Fix Complete!</h6>
        <p class='mb-0'>
            • The sidebar should now show 'Members' instead of 's'<br>
            • All terminology functions are working correctly<br>
            • You can now navigate back to the <a href='members.php' class='alert-link'>Members page</a>
        </p>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='members.php' class='btn btn-primary'>Go to Members Page</a>
                        <a href='settings.php' class='btn btn-outline-primary'>Go to Settings</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
