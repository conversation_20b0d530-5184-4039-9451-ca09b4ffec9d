<?php
/**
 * Debug <PERSON><PERSON> <PERSON>'s Image Data
 */

require_once 'config.php';

echo "<h2>🔍 Debug <PERSON><PERSON>'s Image</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .debug { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .image-test { text-align: center; margin: 20px 0; }
</style>";

try {
    // Find Churks Mike
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Churks%' OR full_name LIKE '%Mike%' ORDER BY full_name");
    $stmt->execute();
    $members = $stmt->fetchAll();
    
    if ($members) {
        echo "<div class='debug'>";
        echo "<h3>👤 Members with 'Churks' or 'Mike':</h3>";
        
        foreach ($members as $member) {
            echo "<hr>";
            echo "<h4>{$member['full_name']} (ID: {$member['id']})</h4>";
            echo "<strong>Email:</strong> {$member['email']}<br>";
            echo "<strong>Image Path:</strong> " . ($member['image_path'] ?: 'NULL/Empty') . "<br>";
            
            // Test image URL generation
            $siteUrl = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            
            if (!empty($member['image_path'])) {
                $imageUrl = $siteUrl . '/' . ltrim($member['image_path'], '/');
                echo "<strong>Generated Image URL:</strong> $imageUrl<br>";
                
                // Test if image file exists
                $localPath = ltrim($member['image_path'], '/');
                if (file_exists($localPath)) {
                    echo "<span class='success'>✅ Image file exists locally at: $localPath</span><br>";
                } else {
                    echo "<span class='error'>❌ Image file not found at: $localPath</span><br>";
                }
                
                // Test HTML generation
                $imageHtml = '<img src="' . $imageUrl . '" alt="' . htmlspecialchars($member['full_name']) . '" style="display:block; max-width:150px; height:auto; border-radius:50%; margin:15px auto;">';
                echo "<strong>Generated HTML:</strong><br>";
                echo "<div style='background: #e9ecef; padding: 10px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($imageHtml) . "</div>";
                
                // Test actual image display
                echo "<div class='image-test'>";
                echo "<strong>Actual Image Display:</strong><br>";
                echo $imageHtml;
                echo "</div>";
            } else {
                $defaultUrl = $siteUrl . '/assets/img/default-profile.jpg';
                echo "<strong>Using Default Image:</strong> $defaultUrl<br>";
                
                // Test if default exists
                if (file_exists('assets/img/default-profile.jpg')) {
                    echo "<span class='success'>✅ Default image exists</span><br>";
                } else {
                    echo "<span class='error'>❌ Default image not found</span><br>";
                }
                
                echo "<div class='image-test'>";
                echo "<strong>Default Image Display:</strong><br>";
                echo "<img src='$defaultUrl' alt='Default Profile' style='max-width:100px; height:auto; border-radius:50%; border: 2px solid #ddd;'>";
                echo "</div>";
            }
        }
        
        echo "</div>";
        
    } else {
        echo "<p class='error'>❌ No members found with 'Churks' or 'Mike' in name</p>";
    }
    
    // Check the specific template that might be used
    echo "<div class='debug'>";
    echo "<h3>🎯 Template Content Check:</h3>";
    
    $stmt = $pdo->query("SELECT id, template_name, content FROM email_templates WHERE is_birthday_template = 1 LIMIT 1");
    $template = $stmt->fetch();
    
    if ($template) {
        echo "<h4>Template: {$template['template_name']}</h4>";
        
        // Check for image placeholders
        $content = $template['content'];
        $placeholders = [];
        
        if (strpos($content, '{member_image}') !== false) {
            $placeholders[] = '{member_image}';
        }
        if (strpos($content, '{birthday_member_image}') !== false) {
            $placeholders[] = '{birthday_member_image}';
        }
        if (strpos($content, '{member_image_url}') !== false) {
            $placeholders[] = '{member_image_url}';
        }
        
        if (!empty($placeholders)) {
            echo "<p class='success'>✅ Found image placeholders: " . implode(', ', $placeholders) . "</p>";
        } else {
            echo "<p class='error'>❌ No image placeholders found in template</p>";
        }
        
        // Show a snippet of the template content
        echo "<strong>Template Content (first 300 chars):</strong><br>";
        echo "<div style='background: #e9ecef; padding: 10px; font-family: monospace; font-size: 12px;'>";
        echo htmlspecialchars(substr($content, 0, 300)) . "...";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>🎯 Debug Complete</h3>";
?>
