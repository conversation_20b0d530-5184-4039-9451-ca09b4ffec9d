<?php
/**
 * Test Page for Contacts Bulk Delete Functionality
 * 
 * This page tests the bulk delete functionality specifically for contacts
 * to ensure it's working correctly after the JavaScript refactoring.
 */

session_start();

// Include configuration
require_once '../config.php';

// Set admin session for testing (remove in production)
$_SESSION['admin_id'] = 1;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contacts Bulk Delete</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Contacts Bulk Delete Test</h1>
        <p class="text-muted">Testing bulk delete functionality for contacts page</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>JavaScript Function Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testJavaScriptFunctions()">Test JS Functions</button>
                        <div id="jsTestResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>AJAX Endpoint Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-warning" onclick="testAjaxEndpoint()">Test AJAX</button>
                        <div id="ajaxTestResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Mock Contacts Table</h5>
                        <p class="mb-0 text-muted">Test the bulk delete UI with mock data</p>
                    </div>
                    <div class="card-body">
                        <!-- Bulk Actions -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="me-3" id="bulkActionsContainer" style="display: none;">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-danger" id="bulkDeleteBtn">
                                        <i class="bi bi-trash me-1"></i>Delete Selected (<span id="selectedCount">0</span>)
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="clearSelectionBtn">
                                        <i class="bi bi-x-circle me-1"></i>Clear Selection
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mock Table -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <input type="checkbox" class="form-check-input" id="selectAllContacts" title="Select All">
                                        </th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input contact-checkbox"
                                                   value="999991"
                                                   data-name="Test Contact 1"
                                                   data-email="<EMAIL>">
                                        </td>
                                        <td>Test Contact 1</td>
                                        <td><EMAIL></td>
                                        <td>(*************</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input contact-checkbox"
                                                   value="999992"
                                                   data-name="Test Contact 2"
                                                   data-email="<EMAIL>">
                                        </td>
                                        <td>Test Contact 2</td>
                                        <td><EMAIL></td>
                                        <td>(555) 234-5678</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input contact-checkbox"
                                                   value="999993"
                                                   data-name="Test Contact 3"
                                                   data-email="<EMAIL>">
                                        </td>
                                        <td>Test Contact 3</td>
                                        <td><EMAIL></td>
                                        <td>(555) 345-6789</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Links</h5>
                    </div>
                    <div class="card-body">
                        <a href="contacts.php" class="btn btn-primary me-2">Go to Contacts Page</a>
                        <a href="members.php" class="btn btn-secondary me-2">Go to Members Page</a>
                        <a href="test_bulk_delete.php" class="btn btn-info">Full Test Suite</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Delete Confirmation Modal -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle me-2"></i>Confirm Bulk Contact Deletion
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-shield-exclamation me-2"></i>⚠️ DANGER: This action cannot be undone!</h6>
                        <p class="mb-0">You are about to permanently delete the selected contacts and all their associated data.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Selected Contacts (<span id="bulkDeleteCount">0</span>):</h6>
                        <div id="bulkDeletePreview" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <!-- Contact list will be populated here -->
                        </div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmBulkDelete">
                        <label class="form-check-label text-danger fw-bold" for="confirmBulkDelete">
                            I understand this action is permanent and cannot be undone
                        </label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bulkDeleteConfirmText" class="form-label">
                            Type <strong>DELETE</strong> to confirm:
                        </label>
                        <input type="text" class="form-control" id="bulkDeleteConfirmText" placeholder="Type DELETE here">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn" disabled>
                        <i class="bi bi-trash me-2"></i>Delete Selected Contacts
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/contacts.js"></script>
    <script>
        // Test JavaScript functions
        function testJavaScriptFunctions() {
            const results = document.getElementById('jsTestResults');
            let html = '<div class="alert alert-info">Running JavaScript tests...</div>';
            
            // Test 1: Check if initializeBulkDelete function exists
            if (typeof window.initializeBulkDelete === 'function') {
                html += '<div class="alert alert-success">✓ initializeBulkDelete function exists</div>';
                
                // Test 2: Initialize the function
                try {
                    window.initializeBulkDelete();
                    html += '<div class="alert alert-success">✓ initializeBulkDelete executed successfully</div>';
                } catch (error) {
                    html += '<div class="alert alert-danger">✗ Error initializing: ' + error.message + '</div>';
                }
            } else {
                html += '<div class="alert alert-danger">✗ initializeBulkDelete function not found</div>';
            }
            
            // Test 3: Check if DOM elements exist
            const elements = [
                'selectAllContacts',
                'bulkActionsContainer', 
                'bulkDeleteBtn',
                'clearSelectionBtn',
                'bulkDeleteModal'
            ];
            
            elements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    html += '<div class="alert alert-success">✓ Element found: ' + elementId + '</div>';
                } else {
                    html += '<div class="alert alert-warning">⚠ Element not found: ' + elementId + '</div>';
                }
            });
            
            results.innerHTML = html;
        }
        
        // Test AJAX endpoint
        function testAjaxEndpoint() {
            const results = document.getElementById('ajaxTestResults');
            results.innerHTML = '<div class="alert alert-info">Testing AJAX endpoint...</div>';
            
            // Test with non-existent IDs for safety
            fetch('ajax/bulk_delete_contacts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contact_ids: [999999, 999998],
                    confirmation_token: 'BULK_DELETE_CONFIRMED'
                })
            })
            .then(response => response.json())
            .then(data => {
                let html = '<div class="alert alert-success">✓ AJAX request successful</div>';
                html += '<div class="alert alert-info">Response: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
                results.innerHTML = html;
            })
            .catch(error => {
                results.innerHTML = '<div class="alert alert-danger">✗ AJAX error: ' + error.message + '</div>';
            });
        }
        
        // Initialize bulk delete when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof window.initializeBulkDelete === 'function') {
                window.initializeBulkDelete();
            }
        });
    </script>
</body>
</html>
