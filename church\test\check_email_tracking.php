<?php
require_once '../config.php';

$conn = $pdo;

echo "<h1>Email Tracking Table Analysis</h1>";

try {
    // Check if email_tracking table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'email_tracking'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ email_tracking table does not exist</p>";
        echo "<p>This means we don't need to delete from this table.</p>";
    } else {
        echo "<p style='color: green;'>✅ email_tracking table exists</p>";
        
        // Get table structure
        $stmt = $conn->query("DESCRIBE email_tracking");
        $columns = $stmt->fetchAll();
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . $column['Field'] . "</strong></td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for sample data
        $stmt = $conn->query("SELECT COUNT(*) as count FROM email_tracking");
        $count = $stmt->fetchColumn();
        echo "<p>Records in table: <strong>$count</strong></p>";
        
        if ($count > 0) {
            echo "<h3>Sample Records:</h3>";
            $stmt = $conn->query("SELECT * FROM email_tracking LIMIT 5");
            $samples = $stmt->fetchAll();
            
            if (!empty($samples)) {
                echo "<table border='1' style='border-collapse: collapse;'>";
                // Header
                echo "<tr>";
                foreach (array_keys($samples[0]) as $column) {
                    if (!is_numeric($column)) {
                        echo "<th>$column</th>";
                    }
                }
                echo "</tr>";
                
                // Data
                foreach ($samples as $row) {
                    echo "<tr>";
                    foreach ($row as $key => $value) {
                        if (!is_numeric($key)) {
                            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                        }
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

// Also check contact_email_logs table
echo "<hr><h2>Contact Email Logs Table Analysis</h2>";

try {
    $stmt = $conn->query("SHOW TABLES LIKE 'contact_email_logs'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>⚠️ contact_email_logs table does not exist</p>";
    } else {
        echo "<p style='color: green;'>✅ contact_email_logs table exists</p>";
        
        $stmt = $conn->query("DESCRIBE contact_email_logs");
        $columns = $stmt->fetchAll();
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . $column['Field'] . "</strong></td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking contact_email_logs: " . $e->getMessage() . "</p>";
}

echo "<h2>Recommendation</h2>";
echo "<p>Based on the analysis above, we need to:</p>";
echo "<ol>";
echo "<li>Remove references to non-existent tables/columns</li>";
echo "<li>Only delete from tables that actually exist and have the correct column names</li>";
echo "<li>Update both individual and bulk delete code accordingly</li>";
echo "</ol>";
?>
