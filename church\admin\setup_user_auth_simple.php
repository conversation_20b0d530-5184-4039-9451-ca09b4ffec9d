<?php
/**
 * Simple User Authentication Schema Setup
 * 
 * This script safely applies the user authentication database schema changes
 */

session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include configuration
require_once '../config.php';
require_once '../classes/SecurityManager.php';

// Initialize SecurityManager
$security = new SecurityManager($pdo);
$security->setSecurityHeaders();

$messages = [];
$errors = [];
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_schema'])) {
    try {
        // Begin transaction
        $pdo->beginTransaction();
        
        // Define columns to add to members table
        $membersColumns = [
            'password_hash' => "varchar(255) DEFAULT NULL COMMENT 'Hashed password for user authentication'",
            'password_reset_token' => "varchar(255) DEFAULT NULL COMMENT 'Token for password reset functionality'",
            'password_reset_expires' => "datetime DEFAULT NULL COMMENT 'Expiration time for password reset token'",
            'last_login_at' => "datetime DEFAULT NULL COMMENT 'Timestamp of last successful login'",
            'failed_login_attempts' => "int(11) NOT NULL DEFAULT 0 COMMENT 'Number of failed login attempts'",
            'account_locked_until' => "datetime DEFAULT NULL COMMENT 'Account lockout expiration time'",
            'must_change_password' => "tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Flag requiring password change on next login'",
            'is_active' => "tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Account active status'",
            'image_path' => "varchar(255) DEFAULT NULL COMMENT 'Profile image file path'"
        ];
        
        // Check existing columns in members table
        $stmt = $pdo->query("DESCRIBE members");
        $existingColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
        
        // Add missing columns to members table
        foreach ($membersColumns as $columnName => $columnDefinition) {
            if (!in_array($columnName, $existingColumns)) {
                try {
                    $sql = "ALTER TABLE `members` ADD COLUMN `$columnName` $columnDefinition";
                    $pdo->exec($sql);
                    $messages[] = "✓ Added column: members.$columnName";
                } catch (PDOException $e) {
                    $messages[] = "⚠ Failed to add column members.$columnName: " . $e->getMessage();
                }
            } else {
                $messages[] = "• Column already exists: members.$columnName";
            }
        }
        
        // Create user_sessions table
        $userSessionsSQL = "
        CREATE TABLE IF NOT EXISTS `user_sessions` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `session_token` varchar(255) NOT NULL,
          `ip_address` varchar(45) DEFAULT NULL,
          `user_agent` varchar(500) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `expires_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `is_active` tinyint(1) NOT NULL DEFAULT 1,
          PRIMARY KEY (`id`),
          KEY `idx_user_sessions_user_id` (`user_id`),
          KEY `idx_user_sessions_token` (`session_token`),
          KEY `idx_user_sessions_expires` (`expires_at`),
          CONSTRAINT `fk_user_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($userSessionsSQL);
            $messages[] = "✓ Created table: user_sessions";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                $messages[] = "• Table already exists: user_sessions";
            } else {
                $messages[] = "⚠ Failed to create user_sessions: " . $e->getMessage();
            }
        }
        
        // Create user_activity_log table
        $userActivitySQL = "
        CREATE TABLE IF NOT EXISTS `user_activity_log` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) DEFAULT NULL,
          `activity_type` varchar(50) NOT NULL,
          `description` text DEFAULT NULL,
          `ip_address` varchar(45) DEFAULT NULL,
          `user_agent` varchar(500) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `idx_user_activity_user_id` (`user_id`),
          KEY `idx_user_activity_type` (`activity_type`),
          KEY `idx_user_activity_created` (`created_at`),
          CONSTRAINT `fk_user_activity_user_id` FOREIGN KEY (`user_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($userActivitySQL);
            $messages[] = "✓ Created table: user_activity_log";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                $messages[] = "• Table already exists: user_activity_log";
            } else {
                $messages[] = "⚠ Failed to create user_activity_log: " . $e->getMessage();
            }
        }
        
        // Create user_preferences table
        $userPreferencesSQL = "
        CREATE TABLE IF NOT EXISTS `user_preferences` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `preference_key` varchar(100) NOT NULL,
          `preference_value` text DEFAULT NULL,
          `value_type` enum('string','boolean','integer','json') NOT NULL DEFAULT 'string',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_user_preference` (`user_id`,`preference_key`),
          KEY `idx_user_preferences_user_id` (`user_id`),
          KEY `idx_user_preferences_key` (`preference_key`),
          CONSTRAINT `fk_user_preferences_user_id` FOREIGN KEY (`user_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($userPreferencesSQL);
            $messages[] = "✓ Created table: user_preferences";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                $messages[] = "• Table already exists: user_preferences";
            } else {
                $messages[] = "⚠ Failed to create user_preferences: " . $e->getMessage();
            }
        }
        
        // Create events table
        $eventsSQL = "
        CREATE TABLE IF NOT EXISTS `events` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `title` varchar(255) NOT NULL,
          `description` text DEFAULT NULL,
          `event_date` datetime NOT NULL,
          `location` varchar(255) DEFAULT NULL,
          `created_by` int(11) DEFAULT NULL,
          `max_attendees` int(11) DEFAULT NULL,
          `is_active` tinyint(1) NOT NULL DEFAULT 1,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `idx_events_date` (`event_date`),
          KEY `idx_events_created_by` (`created_by`),
          KEY `idx_events_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($eventsSQL);
            $messages[] = "✓ Created table: events";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                $messages[] = "• Table already exists: events";
            } else {
                $messages[] = "⚠ Failed to create events: " . $e->getMessage();
            }
        }
        
        // Create event_rsvps table
        $eventRsvpsSQL = "
        CREATE TABLE IF NOT EXISTS `event_rsvps` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `event_id` int(11) NOT NULL,
          `user_id` int(11) NOT NULL,
          `status` enum('attending','not_attending','maybe') NOT NULL DEFAULT 'attending',
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_event_user_rsvp` (`event_id`,`user_id`),
          KEY `idx_event_rsvps_event_id` (`event_id`),
          KEY `idx_event_rsvps_user_id` (`user_id`),
          KEY `idx_event_rsvps_status` (`status`),
          CONSTRAINT `fk_event_rsvps_event_id` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE,
          CONSTRAINT `fk_event_rsvps_user_id` FOREIGN KEY (`user_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($eventRsvpsSQL);
            $messages[] = "✓ Created table: event_rsvps";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                $messages[] = "• Table already exists: event_rsvps";
            } else {
                $messages[] = "⚠ Failed to create event_rsvps: " . $e->getMessage();
            }
        }
        
        // Create birthday_templates table
        $birthdayTemplatesSQL = "
        CREATE TABLE IF NOT EXISTS `birthday_templates` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(255) NOT NULL,
          `subject` varchar(255) NOT NULL,
          `message` text NOT NULL,
          `is_default` tinyint(1) NOT NULL DEFAULT 0,
          `is_active` tinyint(1) NOT NULL DEFAULT 1,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `idx_birthday_templates_default` (`is_default`),
          KEY `idx_birthday_templates_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $pdo->exec($birthdayTemplatesSQL);
            $messages[] = "✓ Created table: birthday_templates";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                $messages[] = "• Table already exists: birthday_templates";
            } else {
                $messages[] = "⚠ Failed to create birthday_templates: " . $e->getMessage();
            }
        }
        
        // Create indexes for performance
        $indexes = [
            "CREATE INDEX IF NOT EXISTS `idx_members_email` ON `members` (`email`)",
            "CREATE INDEX IF NOT EXISTS `idx_members_phone` ON `members` (`phone_number`)",
            "CREATE INDEX IF NOT EXISTS `idx_members_password_reset` ON `members` (`password_reset_token`)",
            "CREATE INDEX IF NOT EXISTS `idx_members_status` ON `members` (`status`)"
        ];
        
        foreach ($indexes as $indexSQL) {
            try {
                $pdo->exec($indexSQL);
                $messages[] = "✓ Created index";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'already exists') === false && strpos($e->getMessage(), 'Duplicate key') === false) {
                    $messages[] = "⚠ Index creation warning: " . $e->getMessage();
                }
            }
        }
        
        // Commit transaction
        $pdo->commit();
        $success = true;
        $messages[] = "✓ Schema setup completed successfully!";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        
        $errors[] = "Error setting up schema: " . $e->getMessage();
        error_log("User authentication schema setup error: " . $e->getMessage());
    }
}

$page_title = 'User Authentication Setup';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Church Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h2><i class="bi bi-database"></i> <?php echo $page_title; ?></h2>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h5><i class="bi bi-exclamation-triangle"></i> Errors</h5>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <h5><i class="bi bi-check-circle"></i> Success</h5>
                                <p class="mb-0">User authentication schema has been set up successfully!</p>
                            </div>
                        <?php endif; ?>

                        <?php if (!$success): ?>
                            <p>This will enhance your existing database with user authentication capabilities.</p>
                            <form method="POST" class="mt-4">
                                <?php echo $security->generateCSRFInput(); ?>
                                <button type="submit" name="setup_schema" class="btn btn-primary">
                                    <i class="bi bi-play-circle"></i> Set Up User Authentication Schema
                                </button>
                            </form>
                        <?php endif; ?>

                        <?php if (!empty($messages)): ?>
                            <div class="mt-4">
                                <h5>Execution Log</h5>
                                <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                                    <?php foreach ($messages as $message): ?>
                                        <div class="font-monospace small"><?php echo htmlspecialchars($message); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mt-4">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                            <?php if ($success): ?>
                                <a href="../test_user_system.php" class="btn btn-success">
                                    <i class="bi bi-check-circle"></i> Run Tests
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
