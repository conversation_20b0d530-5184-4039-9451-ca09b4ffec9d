# Organization-Agnostic System Documentation

## Overview

The Organization-Agnostic System transforms the church management system into a flexible platform that can serve churches, schools, businesses, associations, and other organizations. Instead of hardcoded church-specific terminology, the system uses dynamic placeholders and settings that adapt to any organization type.

## Key Features

### 1. Dynamic Organization Settings
- **Organization Type**: Choose from church, school, business, nonprofit, association, club, community, or other
- **Organization Identity**: Name, mission statement, vision, and core values
- **Custom Terminology**: Define terms for members, leaders, groups, events, and donations

### 2. Automatic Terminology Adjustment
- **Smart Defaults**: Each organization type comes with appropriate default terminology
- **Dynamic Placeholders**: All content uses placeholders that automatically populate with your terms
- **Consistent Experience**: UI labels, email templates, and messages all use your terminology

### 3. Organization Types and Default Terminology

| Organization Type | Member Term | Leader Term | Group Term | Event Term | Donation Term |
|------------------|-------------|-------------|------------|------------|---------------|
| Church           | Member      | Pastor      | Ministry   | Service    | Offering      |
| School           | Student     | Principal   | Class      | Assembly   | Fee           |
| Business         | Employee    | Manager     | Department | Meeting    | Contribution  |
| Nonprofit        | Member      | Director    | Committee  | Event      | Donation      |
| Association      | Member      | President   | Committee  | Meeting    | Dues          |
| Club             | Member      | President   | Group      | Meeting    | Fee           |
| Community        | Participant | Coordinator | Group      | Gathering  | Contribution  |
| Other            | Member      | Leader      | Group      | Event      | Donation      |

## Installation and Setup

### For New Installations
1. Install the system normally
2. Visit **Admin → Site Settings**
3. Configure the "Organization Settings" section
4. Choose your organization type and customize terminology
5. Save settings

### For Existing Installations
1. **Backup your database** before proceeding
2. Run the database migration script:
   ```bash
   php tools/database_migration_v2.php --execute --org-type=church
   ```
3. Run the content migration script:
   ```bash
   php tools/organization_agnostic_migration.php --execute
   ```
4. Visit **Admin → Site Settings** to configure your organization
5. Test the system with the validation script:
   ```bash
   php test/validate_organization_system.php
   ```

## Configuration

### Organization Settings Page
Access via **Admin → Site Settings → Organization Settings**

**Required Fields:**
- **Organization Type**: Select the type that best describes your organization
- **Organization Name**: The full name of your organization

**Optional Fields:**
- **Mission Statement**: Your organization's mission
- **Vision Statement**: Your organization's vision for the future
- **Core Values**: Key values that guide your organization

**Terminology Customization:**
- **Member Term**: What you call your members (e.g., Members, Students, Employees)
- **Leader Term**: What you call your leaders (e.g., Pastor, Principal, Manager)
- **Group Term**: What you call your groups (e.g., Ministry, Department, Team)
- **Event Term**: What you call your events (e.g., Service, Class, Meeting)
- **Donation Term**: What you call donations/payments (e.g., Offering, Fee, Dues)

### Smart Terminology Updates
When you select an organization type, the system automatically suggests appropriate terminology. You can:
- Accept the suggestions by clicking "Yes" in the confirmation dialog
- Customize any terms to match your specific needs
- Use the "Reset to Defaults" button to restore type-specific defaults

## Technical Implementation

### Dynamic Placeholders
The system uses these placeholders throughout templates and content:

**Organization Placeholders:**
- `{organization_name}` - Your organization's name
- `{organization_type}` - Your organization type
- `{organization_mission}` - Mission statement
- `{organization_vision}` - Vision statement
- `{organization_values}` - Core values

**Terminology Placeholders:**
- `{member_term}` - Your member terminology
- `{leader_term}` - Your leader terminology
- `{group_term}` - Your group terminology
- `{event_term}` - Your event terminology
- `{donation_term}` - Your donation terminology

**Legacy Compatibility:**
- `{church_name}` - Maps to `{organization_name}`
- `{church_address}` - Maps to `{organization_address}`
- `{church_email}` - Maps to `{organization_email}`

### Helper Functions
The system provides PHP helper functions for dynamic content:

```php
// Organization information
get_organization_name()     // Returns organization name
get_organization_type()     // Returns organization type
get_site_title()           // Returns dynamic site title
get_admin_title()          // Returns dynamic admin title

// Terminology functions
get_member_term($plural = false)    // Returns member term
get_leader_term($plural = false)    // Returns leader term
get_group_term($plural = false)     // Returns group term
get_event_term($plural = false)     // Returns event term
get_donation_term($plural = false)  // Returns donation term
```

### Database Structure
Organization settings are stored in the `settings` table with these keys:
- `organization_type`
- `organization_name`
- `organization_mission`
- `organization_vision`
- `organization_values`
- `member_term`
- `leader_term`
- `group_term`
- `event_term`
- `donation_term`

## Migration Scripts

### Database Migration Script
**File**: `tools/database_migration_v2.php`

**Purpose**: Adds organization settings to existing installations

**Usage**:
```bash
# Dry run (shows what would be changed)
php tools/database_migration_v2.php

# Execute migration for church
php tools/database_migration_v2.php --execute --org-type=church

# Execute migration for school
php tools/database_migration_v2.php --execute --org-type=school
```

### Content Migration Script
**File**: `tools/organization_agnostic_migration.php`

**Purpose**: Updates existing email templates and content to use dynamic placeholders

**Usage**:
```bash
# Dry run (shows what would be changed)
php tools/organization_agnostic_migration.php

# Execute content migration
php tools/organization_agnostic_migration.php --execute
```

## Testing and Validation

### Validation Script
**File**: `test/validate_organization_system.php`

**Purpose**: Comprehensive testing of the organization-agnostic functionality

**Usage**:
```bash
php test/validate_organization_system.php
```

**Tests Include**:
- Organization settings existence
- Dynamic helper functions
- Placeholder replacement system
- Email template migration status
- Organization type scenarios
- Admin interface integration
- Database schema validation
- Configuration file updates
- Migration tools availability
- Sample content generation

### Test Script
**File**: `test/test_organization_agnostic.php`

**Purpose**: Interactive testing interface accessible via web browser

**Access**: Visit `http://yoursite.com/church/test/test_organization_agnostic.php`

## Troubleshooting

### Common Issues

**1. Placeholders Not Replaced**
- Ensure organization settings are configured in Site Settings
- Check that the `replace_placeholders()` function is working
- Verify database settings are properly saved

**2. Old Hardcoded Terms Still Appearing**
- Run the content migration script: `php tools/organization_agnostic_migration.php --execute`
- Check email templates for remaining hardcoded content
- Clear any cached templates

**3. JavaScript Not Working in Site Settings**
- Verify `organization-type-handler.js` file exists in `admin/assets/js/`
- Check that the JavaScript is included in `site_settings.php`
- Check browser console for JavaScript errors

**4. Migration Script Errors**
- Ensure database connection is working
- Check file permissions for migration scripts
- Review error messages and database logs

### Support Files

**Log Files**:
- Migration scripts create detailed logs in `tools/migration_log_*.json`
- Check `logs/` directory for system logs

**Backup Files**:
- Migration scripts create automatic backups
- Always backup your database before running migrations

## Best Practices

1. **Always backup** your database before running migration scripts
2. **Test thoroughly** after migration with different organization types
3. **Customize terminology** to match your organization's specific language
4. **Update email templates** to use organization-agnostic language
5. **Train administrators** on the new dynamic terminology system
6. **Monitor logs** during and after migration for any issues

## Future Enhancements

The organization-agnostic system is designed to be extensible. Future enhancements may include:
- Additional organization types
- Industry-specific feature sets
- Advanced terminology customization
- Multi-language support for terminology
- Organization-specific themes and branding
