<?php
/**
 * Birthday Templates Page
 * 
 * Allows users to browse and select birthday templates
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

// Get birthday templates from admin-created email templates
$templates = [];
$stmt = $pdo->prepare("
    SELECT
        id,
        template_name as name,
        subject as description,
        content as template_content,
        template_category as category,
        NULL as thumbnail_path,
        COALESCE(
            (SELECT COUNT(*) FROM email_template_usage
             WHERE template_id = email_templates.id AND template_type = 'birthday'),
            0
        ) as usage_count
    FROM email_templates
    WHERE is_birthday_template = 1
    ORDER BY template_category, template_name
");
$stmt->execute();
$templates = $stmt->fetchAll();

// Group templates by category
$templatesByCategory = [];
foreach ($templates as $template) {
    $category = $template['category'] ?: 'general';
    if (!isset($templatesByCategory[$category])) {
        $templatesByCategory[$category] = [];
    }
    $templatesByCategory[$category][] = $template;
}

// Get today's birthdays for quick access
$todaysBirthdays = [];
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, image_path
    FROM members 
    WHERE status = 'active' 
    AND id != ? 
    AND MONTH(birth_date) = MONTH(CURDATE()) 
    AND DAY(birth_date) = DAY(CURDATE())
    ORDER BY full_name
");
$stmt->execute([$userId]);
$todaysBirthdays = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Templates - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: white !important;
        }

        .container {
            max-width: 1200px;
        }
        
        .template-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }

        .template-preview {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            max-height: 200px;
            overflow: hidden;
            position: relative;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .template-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(transparent, #f8f9fa);
        }

        .template-card h6 {
            font-weight: 600;
            color: #2c3e50;
        }

        .template-card .card-footer {
            margin-top: auto;
            padding-top: 1rem;
            border-top: 1px solid #e9ecef;
        }
        
        .category-badge {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .birthday-quick-access {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.5rem 1rem;
            font-weight: 600;
        }

        .btn-outline-primary {
            border-color: var(--bs-primary, #667eea);
            color: var(--bs-primary, #667eea);
            border-radius: var(--bs-border-radius, 10px);
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border-color: var(--bs-primary, #667eea);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <?php
                // Use the existing logo management system
                $headerLogo = get_site_setting('header_logo', '');
                $mainLogo = get_site_setting('main_logo', '');
                $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;
                if (!empty($logoToUse)): ?>
                    <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                         alt="<?php echo get_organization_name(); ?>"
                         class="navbar-logo me-2">
                    <?php echo htmlspecialchars(get_organization_name()); ?>
                <?php else: ?>
                    <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
                <?php endif; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Empty left side to push everything to the right -->
                <div class="navbar-nav me-auto"></div>

                <!-- Main navigation items on the right -->
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-gift"></i> Birthday Templates</h2>
                <p class="text-muted">Choose from beautiful templates to send personalized birthday messages to your church family.</p>
            </div>
        </div>

        <!-- Today's Birthdays Quick Access -->
        <?php if (!empty($todaysBirthdays)): ?>
        <div class="birthday-quick-access">
            <h5><i class="bi bi-calendar-heart"></i> Send Birthday Wishes Today</h5>
            <p class="mb-3">These members are celebrating their birthday today!</p>
            <div class="d-flex flex-wrap gap-2">
                <?php foreach ($todaysBirthdays as $birthday): ?>
                <a href="send_birthday_message.php?member_id=<?php echo $birthday['id']; ?>" 
                   class="btn btn-light btn-sm">
                    <i class="bi bi-envelope-heart"></i> <?php echo htmlspecialchars($birthday['first_name']); ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Template Categories -->
        <?php if (empty($templatesByCategory)): ?>
        <div class="text-center py-5">
            <i class="bi bi-gift display-1 text-muted"></i>
            <h4 class="mt-3">No Templates Available</h4>
            <p class="text-muted">Birthday templates will appear here once they are created by administrators.</p>
        </div>
        <?php else: ?>
            <?php foreach ($templatesByCategory as $category => $categoryTemplates): ?>
            <div class="mb-5">
                <h4 class="mb-3">
                    <span class="category-badge"><?php echo ucfirst($category); ?></span>
                    Templates
                </h4>
                
                <div class="row">
                    <?php foreach ($categoryTemplates as $template): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="template-card">
                            <div class="template-preview">
                                <?php
                                // Create a sample preview of the template
                                $previewContent = $template['template_content'];

                                // Replace both old and new placeholder formats
                                $replacements = [
                                    // New format (curly braces)
                                    '{full_name}' => 'John Doe',
                                    '{first_name}' => 'John',
                                    '{last_name}' => 'Doe',
                                    '{organization_name}' => $sitename,
                                    '{organization_type}' => 'church',
                                    '{sender_name}' => $userData['first_name'],
                                    '{sender_full_name}' => $userData['first_name'] . ' ' . ($userData['last_name'] ?? ''),

                                    // Legacy format (double curly braces)
                                    '{{recipient_name}}' => 'John Doe',
                                    '{{sender_name}}' => $userData['first_name'],
                                    '{{church_name}}' => $sitename,

                                    // Old format (square brackets)
                                    '[name]' => 'John Doe',
                                    '[sender]' => $userData['first_name'],
                                    '[church]' => $sitename
                                ];

                                foreach ($replacements as $placeholder => $value) {
                                    $previewContent = str_replace($placeholder, $value, $previewContent);
                                }

                                // Limit preview content length for card display
                                if (strlen($previewContent) > 400) {
                                    $previewContent = substr($previewContent, 0, 400) . '...';
                                }

                                echo $previewContent;
                                ?>
                            </div>
                            
                            <h6 class="mb-2"><?php echo htmlspecialchars($template['name']); ?></h6>
                            
                            <?php if ($template['description']): ?>
                            <p class="text-muted small mb-3"><?php echo htmlspecialchars($template['description']); ?></p>
                            <?php endif; ?>
                            
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="bi bi-heart"></i> Used <?php echo $template['usage_count']; ?> times
                                    </small>
                                    <div>
                                        <button class="btn btn-outline-primary btn-sm me-2"
                                                onclick="previewTemplate(<?php echo $template['id']; ?>)">
                                            <i class="bi bi-eye"></i> Preview
                                        </button>
                                        <a href="send_birthday_message.php?template_id=<?php echo $template['id']; ?>"
                                           class="btn btn-primary btn-sm">
                                            <i class="bi bi-envelope"></i> Use Template
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Template Preview Modal -->
    <div class="modal fade" id="templatePreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Template Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="templatePreviewContent">
                    <!-- Preview content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="useTemplateBtn">Use This Template</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewTemplate(templateId) {
            // Load template preview via AJAX
            fetch('ajax/preview_template.php?id=' + templateId)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('templatePreviewContent').innerHTML = html;
                    document.getElementById('useTemplateBtn').onclick = function() {
                        window.location.href = 'send_birthday_message.php?template_id=' + templateId;
                    };
                    new bootstrap.Modal(document.getElementById('templatePreviewModal')).show();
                })
                .catch(error => {
                    console.error('Error loading template preview:', error);
                    alert('Error loading template preview. Please try again.');
                });
        }
    </script>
</body>
</html>
