<?php
/**
 * Organization-Agnostic System Validation Script
 * 
 * Comprehensive testing of the organization-agnostic functionality
 * Tests all dynamic functions, placeholder replacements, and system integration
 */

require_once '../config.php';

// Test configuration
$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "Running: $testName... ";
    
    try {
        $result = $testFunction();
        if ($result['success']) {
            echo "✓ PASS\n";
            $passedTests++;
            $testResults[$testName] = ['status' => 'PASS', 'message' => $result['message']];
        } else {
            echo "✗ FAIL - " . $result['message'] . "\n";
            $testResults[$testName] = ['status' => 'FAIL', 'message' => $result['message']];
        }
    } catch (Exception $e) {
        echo "✗ ERROR - " . $e->getMessage() . "\n";
        $testResults[$testName] = ['status' => 'ERROR', 'message' => $e->getMessage()];
    }
}

echo "=== Organization-Agnostic System Validation ===\n\n";

// Test 1: Organization Settings Exist
runTest("Organization Settings Exist", function() {
    $requiredSettings = [
        'organization_type', 'organization_name', 'member_term', 
        'leader_term', 'group_term', 'event_term', 'donation_term'
    ];
    
    $missingSettings = [];
    foreach ($requiredSettings as $setting) {
        $value = get_site_setting($setting, null);
        if ($value === null) {
            $missingSettings[] = $setting;
        }
    }
    
    if (empty($missingSettings)) {
        return ['success' => true, 'message' => 'All required organization settings exist'];
    } else {
        return ['success' => false, 'message' => 'Missing settings: ' . implode(', ', $missingSettings)];
    }
});

// Test 2: Dynamic Helper Functions
runTest("Dynamic Helper Functions", function() {
    $functions = [
        'get_organization_name' => get_organization_name(),
        'get_organization_type' => get_organization_type(),
        'get_site_title' => get_site_title(),
        'get_admin_title' => get_admin_title(),
        'get_member_term' => get_member_term(),
        'get_leader_term' => get_leader_term(),
        'get_group_term' => get_group_term(),
        'get_event_term' => get_event_term(),
        'get_donation_term' => get_donation_term(),
    ];
    
    $emptyFunctions = [];
    foreach ($functions as $funcName => $result) {
        if (empty($result) || $result === 'Organization' || $result === 'Leader') {
            $emptyFunctions[] = $funcName;
        }
    }
    
    if (empty($emptyFunctions)) {
        return ['success' => true, 'message' => 'All helper functions return valid values'];
    } else {
        return ['success' => false, 'message' => 'Functions returning default/empty values: ' . implode(', ', $emptyFunctions)];
    }
});

// Test 3: Placeholder Replacement System
runTest("Placeholder Replacement System", function() {
    $testContent = "Welcome to {organization_name}! We are a {organization_type} serving {member_term}s.";
    $replacedContent = replace_placeholders($testContent, []);
    
    // Check if placeholders were replaced (should not contain curly braces)
    if (strpos($replacedContent, '{') === false && strpos($replacedContent, '}') === false) {
        return ['success' => true, 'message' => 'Placeholders successfully replaced'];
    } else {
        return ['success' => false, 'message' => 'Placeholders not replaced: ' . $replacedContent];
    }
});

// Test 4: Email Templates Migration Status
runTest("Email Templates Migration Status", function() {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM email_templates");
    $stmt->execute();
    $totalTemplates = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($totalTemplates == 0) {
        return ['success' => true, 'message' => 'No email templates to check'];
    }
    
    // Check for old hardcoded terms
    $oldTerms = ['Freedom Assembly Church', 'Church Team', 'Blessings,', 'church family'];
    $problemTemplates = 0;
    
    foreach ($oldTerms as $term) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM email_templates WHERE content LIKE ? OR subject LIKE ?");
        $stmt->execute(["%$term%", "%$term%"]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $problemTemplates += $count;
    }
    
    if ($problemTemplates == 0) {
        return ['success' => true, 'message' => "All $totalTemplates email templates appear to be migrated"];
    } else {
        return ['success' => false, 'message' => "$problemTemplates templates still contain old hardcoded terms"];
    }
});

// Test 5: Organization Type Scenarios
runTest("Organization Type Scenarios", function() {
    $scenarios = ['church', 'school', 'business', 'nonprofit'];
    $currentType = get_organization_type();
    
    if (in_array($currentType, $scenarios)) {
        return ['success' => true, 'message' => "Current organization type '$currentType' is supported"];
    } else {
        return ['success' => false, 'message' => "Current organization type '$currentType' may need additional configuration"];
    }
});

// Test 6: Admin Interface Integration
runTest("Admin Interface Integration", function() {
    // Test if the organization-type-handler.js file exists
    $jsFile = __DIR__ . '/../admin/assets/js/organization-type-handler.js';
    if (!file_exists($jsFile)) {
        return ['success' => false, 'message' => 'organization-type-handler.js file not found'];
    }
    
    // Test if site_settings.php includes the JavaScript
    $settingsFile = file_get_contents(__DIR__ . '/../admin/site_settings.php');
    if (strpos($settingsFile, 'organization-type-handler.js') === false) {
        return ['success' => false, 'message' => 'JavaScript not included in site_settings.php'];
    }
    
    return ['success' => true, 'message' => 'Admin interface integration appears correct'];
});

// Test 7: Database Schema Validation
runTest("Database Schema Validation", function() {
    global $pdo;
    
    // Check if settings table can handle organization settings
    try {
        $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'organization_%' LIMIT 1");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return ['success' => true, 'message' => 'Database schema supports organization settings'];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database schema issue: ' . $e->getMessage()];
    }
});

// Test 8: Configuration File Updates
runTest("Configuration File Updates", function() {
    // Check if config.php has organization-agnostic placeholders
    $configFile = file_get_contents(__DIR__ . '/../config.php');
    
    $requiredPlaceholders = [
        '{organization_name}', '{member_term}', '{leader_term}', 
        'get_organization_name()', 'get_member_term()', 'get_leader_term()'
    ];
    
    $missingPlaceholders = [];
    foreach ($requiredPlaceholders as $placeholder) {
        if (strpos($configFile, $placeholder) === false) {
            $missingPlaceholders[] = $placeholder;
        }
    }
    
    if (empty($missingPlaceholders)) {
        return ['success' => true, 'message' => 'Configuration file has been updated with organization-agnostic placeholders'];
    } else {
        return ['success' => false, 'message' => 'Missing placeholders in config.php: ' . implode(', ', $missingPlaceholders)];
    }
});

// Test 9: Migration Tools Availability
runTest("Migration Tools Availability", function() {
    $migrationScript = __DIR__ . '/../tools/database_migration_v2.php';
    $orgMigrationScript = __DIR__ . '/../tools/organization_agnostic_migration.php';
    
    $missingTools = [];
    if (!file_exists($migrationScript)) {
        $missingTools[] = 'database_migration_v2.php';
    }
    if (!file_exists($orgMigrationScript)) {
        $missingTools[] = 'organization_agnostic_migration.php';
    }
    
    if (empty($missingTools)) {
        return ['success' => true, 'message' => 'All migration tools are available'];
    } else {
        return ['success' => false, 'message' => 'Missing migration tools: ' . implode(', ', $missingTools)];
    }
});

// Test 10: Sample Content Generation
runTest("Sample Content Generation", function() {
    $orgName = get_organization_name();
    $memberTerm = get_member_term();
    $leaderTerm = get_leader_term();
    
    $sampleWelcome = "Welcome to $orgName! As a new $memberTerm, you'll work with our $leaderTerm to participate in our community.";
    
    // Check if sample content looks reasonable (no placeholders, not too generic)
    if (strpos($sampleWelcome, '{') === false && 
        $orgName !== 'Organization' && 
        $memberTerm !== 'Member' && 
        $leaderTerm !== 'Leader') {
        return ['success' => true, 'message' => 'Sample content generation works correctly'];
    } else {
        return ['success' => false, 'message' => 'Sample content still contains defaults or placeholders'];
    }
});

// Display Results
echo "\n=== Test Results Summary ===\n";
echo "Total Tests: $totalTests\n";
echo "Passed: $passedTests\n";
echo "Failed: " . ($totalTests - $passedTests) . "\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

// Detailed Results
echo "=== Detailed Results ===\n";
foreach ($testResults as $testName => $result) {
    $status = $result['status'];
    $message = $result['message'];
    echo "[$status] $testName: $message\n";
}

// Recommendations
echo "\n=== Recommendations ===\n";
if ($passedTests == $totalTests) {
    echo "✓ All tests passed! The organization-agnostic system is ready for use.\n";
    echo "✓ You can now configure different organization types in Site Settings.\n";
    echo "✓ Consider running the migration scripts if you have existing content to update.\n";
} else {
    echo "⚠ Some tests failed. Please address the issues above before using the system.\n";
    
    if (isset($testResults['Organization Settings Exist']) && $testResults['Organization Settings Exist']['status'] !== 'PASS') {
        echo "• Run the database migration script: php tools/database_migration_v2.php --execute\n";
    }
    
    if (isset($testResults['Email Templates Migration Status']) && $testResults['Email Templates Migration Status']['status'] !== 'PASS') {
        echo "• Run the content migration script: php tools/organization_agnostic_migration.php --execute\n";
    }
    
    echo "• Visit the Site Settings page to configure your organization details\n";
    echo "• Test the system with different organization types\n";
}

echo "\n=== Validation Complete ===\n";
?>
