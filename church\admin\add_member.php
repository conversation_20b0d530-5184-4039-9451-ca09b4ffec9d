<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include UserAuthManager for password generation and user creation
require_once '../classes/UserAuthManager.php';
require_once '../classes/SecurityManager.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize UserAuthManager
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Initialize variables - make sure all form fields are initialized here
$first_name = $last_name = $full_name = $email = $phone_number = $occupation = $home_address = $birth_date = $message = '';
$success_message = $error_message = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Get form data and sanitize
        $full_name = trim($_POST['full_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone_number = trim($_POST['phone_number'] ?? '');
        $occupation = trim($_POST['occupation'] ?? '');
        $home_address = trim($_POST['home_address'] ?? '');
        $birth_date = trim($_POST['birth_date'] ?? '');
        $message = trim($_POST['message'] ?? '');
        
        // Split full name into first and last name
        $name_parts = explode(' ', $full_name, 2);
        $first_name = $name_parts[0];
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';
        
        // Validate required fields
        $errors = [];
        
        if (empty($full_name)) {
            $errors[] = "Full name is required";
        }
        
        if (empty($email)) {
            $errors[] = "Email address is required";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Please enter a valid email address";
        }
        
        if (empty($birth_date)) {
            $errors[] = "Birth date is required";
        } elseif (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $birth_date)) {
            $errors[] = "Birth date must be in YYYY-MM-DD format";
        }
        
        if (!empty($errors)) {
            $error_message = "Please correct the following: <ul><li>" . implode("</li><li>", $errors) . "</li></ul>";
        } else {
            // Check if email already exists
            $stmt = $conn->prepare("SELECT id FROM members WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                $error_message = "This email address is already registered.";
            } else {
                // Process profile image if uploaded
                $profile_image = '';
                if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
                    $filename = $_FILES['profile_image']['name'];
                    $file_ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                    
                    if (in_array($file_ext, $allowed)) {
                        // Create unique filename
                        $new_filename = uniqid() . '.' . $file_ext;
                        $upload_dir = '../uploads/profiles/';

                        // Create directory if it doesn't exist
                        if (!is_dir($upload_dir)) {
                            if (!mkdir($upload_dir, 0755, true)) {
                                throw new Exception("Failed to create uploads directory");
                            }
                        }

                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                            $profile_image = 'uploads/profiles/' . $new_filename;
                        } else {
                            // Continue without image if upload fails
                            $error_message = "Warning: Failed to upload image, but member will be added without profile image.";
                        }
                    }
                }
                
                // Create user account with auto-generated password using UserAuthManager
                $userData = [
                    'full_name' => $full_name,
                    'first_name' => $first_name,
                    'last_name' => $last_name,
                    'email' => $email,
                    'phone_number' => $phone_number,
                    'birth_date' => $birth_date,
                    'home_address' => $home_address,
                    'occupation' => $occupation,
                    'image_path' => $profile_image
                ];

                $result = $userAuth->createUserAccount($userData);

                if ($result['success']) {
                    $member_id = $result['user_id'];
                    $tempPassword = $result['temp_password']; // Get the generated password
                    
                    // Send welcome email with temporary password to the new member
                    $memberData = [
                        'full_name' => $full_name,
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'email' => $email,
                        'phone_number' => $phone_number,
                        'birth_date' => $birth_date,
                        'occupation' => $occupation,
                        'home_address' => $home_address,
                        'image_path' => $profile_image,
                        'temp_password' => $tempPassword,
                        'login_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/campaign/church/user/login.php'
                    ];

                    $emailSent = false;
                    $emailError = '';

                    try {
                        $emailSent = sendAdminCreatedUserWelcomeEmail($memberData);
                        if (!$emailSent && isset($last_email_error)) {
                            $emailError = $last_email_error;
                        }
                    } catch (Exception $e) {
                        $emailError = $e->getMessage();
                    }
                    
                    if ($emailSent) {
                        $success_message = "Member added successfully! A welcome email has been sent to their email address.";
                    } else {
                        $success_message = "Member added successfully!";
                        if (!empty($emailError)) {
                            $error_message = "The welcome email could not be sent: " . $emailError;
                        } else {
                            $error_message = "The welcome email could not be sent. Please check your email settings.";
                        }
                    }
                    
                    // Log the email result
                    $email_status = $emailSent ? 'sent' : 'failed';
                    $log_stmt = $conn->prepare("INSERT INTO email_logs (member_id, template_id, status, sent_at, error_message) VALUES (?, NULL, ?, NOW(), ?)");
                    $log_stmt->execute([$member_id, $email_status, $emailError]);
                    
                    // Clear form data after successful submission
                    $full_name = $first_name = $last_name = $email = $phone_number = $occupation = $home_address = $birth_date = $message = '';
                } else {
                    $error_message = "Error creating user account: " . $result['message'];
                }
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
        error_log("Exception in add_member.php: " . $e->getMessage());
    }
}

// Close the database connection
$conn = null;

// Set page variables
$page_title = 'Add New Member';
$page_header = 'Add New Member';
$page_description = 'Add a new member to the church database.';

// Include header
include 'includes/header.php';

// Display success message if it exists
if (isset($success_message) && !empty($success_message)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>' . htmlspecialchars($success_message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display error message if it exists
if (isset($error_message) && !empty($error_message)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . $error_message . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Add Member Form -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title mb-4">Member Information</h5>
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" enctype="multipart/form-data" class="needs-validation" novalidate>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="full_name" class="form-label required-field">Full Name</label>
                    <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($full_name ?? ''); ?>" required>
                </div>
                <div class="col-md-6">
                    <label for="email" class="form-label required-field">Email Address</label>
                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="phone_number" class="form-label">Phone Number</label>
                    <input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="+27 XX XXX XXXX" value="<?php echo htmlspecialchars($phone_number ?? ''); ?>">
                    <small class="form-text text-muted">Include country code (e.g., +27 for South Africa)</small>
                </div>
                <div class="col-md-6">
                    <label for="occupation" class="form-label">Occupation</label>
                    <input type="text" class="form-control" id="occupation" name="occupation" value="<?php echo htmlspecialchars($occupation ?? ''); ?>">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="home_address" class="form-label">Home Address</label>
                <textarea class="form-control" id="home_address" name="home_address" rows="2"><?php echo htmlspecialchars($home_address ?? ''); ?></textarea>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="birth_date" class="form-label required-field">Birth Date</label>
                    <input type="date" class="form-control" id="birth_date" name="birth_date" value="<?php echo htmlspecialchars($birth_date ?? ''); ?>" required>
                </div>
                <div class="col-md-6">
                    <label for="profile_image" class="form-label">Profile Image</label>
                    <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*" onchange="previewImage(this)">
                    <img id="preview" class="preview-image d-none">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="message" class="form-label">Prayer Requests or Comments</label>
                <textarea class="form-control" id="message" name="message" rows="3"><?php echo htmlspecialchars($message ?? ''); ?></textarea>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="members.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to Members</a>
                <button type="submit" class="btn btn-primary"><i class="bi bi-person-plus me-2"></i>Add Member</button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
    function previewImage(input) {
        const preview = document.getElementById('preview');
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            }
            reader.readAsDataURL(input.files[0]);
        }
    }
</script>
</body>
</html> 