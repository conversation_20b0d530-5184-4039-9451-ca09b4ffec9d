<?php
/**
 * Automated Event Reminder Email System
 * Sends reminder emails to users who have RSVP'd "attending" to events
 * Triggers 24 hours (1 day) before the event date/time
 */

require_once '../config.php';

// Set script timeout for long-running operations
set_time_limit(300); // 5 minutes

// Initialize logging
$logFile = __DIR__ . '/../logs/event_reminders.log';
if (!is_dir(dirname($logFile))) {
    mkdir(dirname($logFile), 0755, true);
}

function logReminder($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND);
}

function sendEventReminderEmail($eventData, $memberData) {
    global $pdo;
    
    try {
        $organizationName = get_organization_name();
        $organizationType = get_organization_type();
        
        // Get event reminder template from database
        $stmt = $pdo->prepare("
            SELECT * FROM email_templates 
            WHERE template_type = 'event_reminder' 
            AND is_active = 1 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute();
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            // Create default event reminder template
            $defaultSubject = "Reminder: {event_title} Tomorrow at {organization_name}";
            $defaultContent = "
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 10px;'>
                    <div style='text-align: center; margin-bottom: 30px;'>
                        <h1 style='color: #2c3e50; margin-bottom: 10px;'>Event Reminder</h1>
                        <p style='color: #7f8c8d; font-size: 16px;'>{organization_name}</p>
                    </div>
                    
                    <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>
                        <h2 style='color: #2c3e50; margin-top: 0;'>{event_title}</h2>
                        <p style='margin: 10px 0; font-size: 16px;'><strong>📅 Date:</strong> {event_date}</p>
                        <p style='margin: 10px 0; font-size: 16px;'><strong>🕐 Time:</strong> {event_time}</p>
                        <p style='margin: 10px 0; font-size: 16px;'><strong>📍 Location:</strong> {event_location}</p>
                        {event_description_section}
                    </div>
                    
                    <div style='margin: 20px 0;'>
                        <p>Dear {member_name},</p>
                        <p>This is a friendly reminder that you have RSVP'd to attend <strong>{event_title}</strong> tomorrow.</p>
                        <p>We're looking forward to seeing you there!</p>
                    </div>
                    
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{event_details_url}' style='background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;'>View Event Details</a>
                    </div>
                    
                    <div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #7f8c8d; font-size: 14px;'>
                        <p>If you can no longer attend, please update your RSVP as soon as possible.</p>
                        <p>Blessings,<br>The {organization_name} Team</p>
                    </div>
                </div>
            ";
            
            $subject = $defaultSubject;
            $content = $defaultContent;
            
            logReminder("Using default event reminder template for event ID: " . $eventData['id']);
        } else {
            $subject = $template['subject'];
            $content = $template['content'];
            logReminder("Using custom event reminder template: " . $template['template_name']);
        }
        
        // Prepare template data
        $eventDateTime = new DateTime($eventData['event_date']);
        $templateData = [
            'member_name' => $memberData['full_name'],
            'first_name' => $memberData['first_name'],
            'last_name' => $memberData['last_name'],
            'email' => $memberData['email'],
            'event_title' => $eventData['title'],
            'event_date' => $eventDateTime->format('l, F j, Y'),
            'event_time' => $eventDateTime->format('g:i A'),
            'event_location' => $eventData['location'],
            'event_description' => $eventData['description'],
            'organization_name' => $organizationName,
            'organization_type' => $organizationType,
            'event_details_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/campaign/church/user/events.php',
            'event_description_section' => !empty($eventData['description']) ? 
                "<p style='margin: 10px 0; font-size: 16px;'><strong>📝 Description:</strong> " . htmlspecialchars($eventData['description']) . "</p>" : ""
        ];
        
        // Process template placeholders
        $processedSubject = replaceTemplatePlaceholders($subject, $templateData);
        $processedContent = replaceTemplatePlaceholders($content, $templateData);
        
        // Send the email
        $emailSent = sendEmail(
            $memberData['email'],
            $memberData['full_name'],
            $processedSubject,
            $processedContent,
            true,
            $templateData
        );
        
        if ($emailSent) {
            logReminder("Event reminder sent successfully to: " . $memberData['email'] . " for event: " . $eventData['title']);
            return true;
        } else {
            logReminder("Failed to send event reminder to: " . $memberData['email'] . " for event: " . $eventData['title']);
            return false;
        }
        
    } catch (Exception $e) {
        logReminder("Error sending event reminder: " . $e->getMessage());
        return false;
    }
}

function processEventReminders() {
    global $pdo;
    
    logReminder("Starting event reminder processing...");
    
    try {
        // Get events that are happening in 24 hours (tomorrow)
        $tomorrow = date('Y-m-d H:i:s', strtotime('+24 hours'));
        $dayAfterTomorrow = date('Y-m-d H:i:s', strtotime('+48 hours'));

        $stmt = $pdo->prepare("
            SELECT e.*, ec.name as category_name
            FROM events e
            LEFT JOIN event_categories ec ON e.category_id = ec.id
            WHERE e.event_date BETWEEN ? AND ?
            ORDER BY e.event_date ASC
        ");
        $stmt->execute([$tomorrow, $dayAfterTomorrow]);
        $upcomingEvents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        logReminder("Found " . count($upcomingEvents) . " events happening in the next 24 hours");
        
        $totalReminders = 0;
        $successfulReminders = 0;
        
        foreach ($upcomingEvents as $event) {
            logReminder("Processing event: " . $event['title'] . " (ID: " . $event['id'] . ")");
            
            // Get all members who RSVP'd "attending" for this event
            $stmt = $pdo->prepare("
                SELECT m.*, er.status, er.notes
                FROM members m
                INNER JOIN event_rsvps er ON m.id = er.user_id
                WHERE er.event_id = ?
                AND er.status = 'attending'
                AND m.email IS NOT NULL
                AND m.email != ''
            ");
            $stmt->execute([$event['id']]);
            $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            logReminder("Found " . count($attendees) . " attendees for event: " . $event['title']);
            
            foreach ($attendees as $attendee) {
                // Check if we've already sent a reminder for this event to this user
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as reminder_count
                    FROM email_logs
                    WHERE recipient_email = ?
                    AND email_type = 'event_reminder'
                    AND related_id = ?
                    AND sent_at >= DATE_SUB(NOW(), INTERVAL 2 DAY)
                ");
                $stmt->execute([$attendee['email'], $event['id']]);
                $reminderCheck = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($reminderCheck['reminder_count'] > 0) {
                    logReminder("Reminder already sent to " . $attendee['email'] . " for event: " . $event['title']);
                    continue;
                }
                
                $totalReminders++;
                
                // Send reminder email
                if (sendEventReminderEmail($event, $attendee)) {
                    $successfulReminders++;
                    
                    // Log the email in email_logs table
                    try {
                        $stmt = $pdo->prepare("
                            INSERT INTO email_logs (recipient_email, recipient_name, subject, email_type, related_id, sent_at, status)
                            VALUES (?, ?, ?, 'event_reminder', ?, NOW(), 'sent')
                        ");
                        $stmt->execute([
                            $attendee['email'],
                            $attendee['full_name'],
                            "Event Reminder: " . $event['title'],
                            $event['id']
                        ]);
                    } catch (Exception $e) {
                        logReminder("Failed to log email for " . $attendee['email'] . ": " . $e->getMessage());
                    }
                }
                
                // Small delay to prevent overwhelming the email server
                usleep(500000); // 0.5 second delay
            }
        }
        
        logReminder("Event reminder processing completed. Sent $successfulReminders out of $totalReminders reminders.");
        
        return [
            'success' => true,
            'total_events' => count($upcomingEvents),
            'total_reminders' => $totalReminders,
            'successful_reminders' => $successfulReminders
        ];
        
    } catch (Exception $e) {
        logReminder("Error in processEventReminders: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Main execution
if (php_sapi_name() === 'cli') {
    // Running from command line (cron job)
    echo "Starting automated event reminder system...\n";
    $result = processEventReminders();
    
    if ($result['success']) {
        echo "Successfully processed {$result['total_events']} events and sent {$result['successful_reminders']} reminders.\n";
    } else {
        echo "Error: {$result['error']}\n";
    }
} else {
    // Running from web interface (for testing)
    header('Content-Type: application/json');
    echo json_encode(processEventReminders());
}
?>
