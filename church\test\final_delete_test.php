<?php
/**
 * Final comprehensive test for contact delete functionality
 * This script tests both individual and bulk delete operations
 */

session_start();
require_once '../config.php';

$conn = $pdo;
$_SESSION['admin_id'] = 1; // Set admin session

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<h1>Final Contact Delete Functionality Test</h1>";

// Test 1: Individual Delete Test
echo "<h2>Test 1: Individual Delete</h2>";

// Create a test contact
try {
    $test_email = 'individual_test_' . time() . '@example.com';
    $test_name = 'Individual Test Contact';
    
    $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
    $stmt->execute([$test_email, $test_name]);
    $contact_id = $conn->lastInsertId();
    
    echo "✅ Created test contact (ID: $contact_id)<br>";
    
    // Test the individual delete process
    $_GET['delete_id'] = $contact_id;
    $_GET['csrf_token'] = $_SESSION['csrf_token'];
    
    // Simulate the delete logic from contacts.php
    if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
        if (!isset($_GET['csrf_token']) || $_GET['csrf_token'] !== $_SESSION['csrf_token']) {
            echo "❌ CSRF validation failed<br>";
        } else {
            $delete_id = intval($_GET['delete_id']);
            
            try {
                $conn->beginTransaction();
                
                // Delete associated records first
                try {
                    $stmt = $conn->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
                    $stmt->execute([$delete_id]);
                } catch (PDOException $e) {
                    // Table might not exist, continue
                }

                $stmt = $conn->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
                $stmt->execute([$delete_id]);
                
                // Delete the contact
                $stmt = $conn->prepare("DELETE FROM contacts WHERE id = ?");
                if ($stmt->execute([$delete_id])) {
                    $conn->commit();
                    echo "✅ Individual delete successful!<br>";
                } else {
                    $conn->rollBack();
                    echo "❌ Individual delete failed<br>";
                }
            } catch (PDOException $e) {
                if ($conn->inTransaction()) {
                    $conn->rollBack();
                }
                echo "❌ Individual delete error: " . $e->getMessage() . "<br>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Error creating test contact: " . $e->getMessage() . "<br>";
}

// Test 2: Bulk Delete Test
echo "<h2>Test 2: Bulk Delete</h2>";

// Create multiple test contacts
$bulk_contact_ids = [];
for ($i = 1; $i <= 3; $i++) {
    try {
        $bulk_email = "bulk_test_{$i}_" . time() . "@example.com";
        $bulk_name = "Bulk Test Contact $i";
        
        $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
        $stmt->execute([$bulk_email, $bulk_name]);
        $bulk_contact_ids[] = $conn->lastInsertId();
        
        echo "✅ Created bulk test contact $i (ID: " . end($bulk_contact_ids) . ")<br>";
    } catch (PDOException $e) {
        echo "❌ Error creating bulk test contact $i: " . $e->getMessage() . "<br>";
    }
}

if (!empty($bulk_contact_ids)) {
    echo "Testing bulk delete for IDs: " . implode(', ', $bulk_contact_ids) . "<br>";
    
    // Simulate bulk delete process
    try {
        $conn->beginTransaction();
        
        // Get contacts to delete
        $placeholders = str_repeat('?,', count($bulk_contact_ids) - 1) . '?';
        $stmt = $conn->prepare("SELECT * FROM contacts WHERE id IN ($placeholders)");
        $stmt->execute($bulk_contact_ids);
        $contacts_to_delete = $stmt->fetchAll();
        
        echo "Found " . count($contacts_to_delete) . " contacts to delete<br>";
        
        $successfully_deleted = 0;
        
        // Delete each contact
        foreach ($contacts_to_delete as $contact) {
            $contact_id = $contact['id'];
            
            try {
                // Delete related records
                try {
                    $stmt = $conn->prepare("DELETE FROM contact_email_logs WHERE recipient_id = ?");
                    $stmt->execute([$contact_id]);
                } catch (PDOException $e) {
                    // Table might not exist, continue
                }

                $stmt = $conn->prepare("DELETE FROM contact_group_members WHERE contact_id = ?");
                $stmt->execute([$contact_id]);
                
                // Delete the contact
                $stmt = $conn->prepare("DELETE FROM contacts WHERE id = ?");
                if ($stmt->execute([$contact_id]) && $stmt->rowCount() > 0) {
                    $successfully_deleted++;
                    echo "✅ Deleted contact ID $contact_id<br>";
                } else {
                    echo "❌ Failed to delete contact ID $contact_id<br>";
                }
            } catch (PDOException $e) {
                echo "❌ Error deleting contact ID $contact_id: " . $e->getMessage() . "<br>";
            }
        }
        
        $conn->commit();
        echo "✅ Bulk delete completed. Successfully deleted: $successfully_deleted contacts<br>";
        
    } catch (PDOException $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        echo "❌ Bulk delete error: " . $e->getMessage() . "<br>";
    }
}

// Test 3: UI Components Test
echo "<h2>Test 3: UI Components</h2>";

// Check if required files exist
$files_to_check = [
    '../admin/contacts.php' => 'Main contacts page',
    '../admin/ajax/bulk_delete_contacts.php' => 'Bulk delete AJAX handler',
    '../admin/js/contacts.js' => 'JavaScript functionality'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description exists<br>";
    } else {
        echo "❌ $description missing<br>";
    }
}

// Test 4: Database Structure
echo "<h2>Test 4: Database Structure</h2>";

$tables_to_check = [
    'contacts' => 'Main contacts table',
    'contact_groups' => 'Contact groups table',
    'contact_group_members' => 'Contact group memberships',
    'contact_email_logs' => 'Contact email logs (optional)',
    'email_tracking' => 'Email tracking (optional)'
];

foreach ($tables_to_check as $table => $description) {
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ $description exists<br>";
        } else {
            echo "⚠️ $description missing (may not be critical)<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Error checking $description: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>Summary</h2>";
echo "<p><strong>Individual Delete:</strong> Uses GET request with CSRF token, same as members page</p>";
echo "<p><strong>Bulk Delete:</strong> Uses AJAX POST request to bulk_delete_contacts.php</p>";
echo "<p><strong>UI:</strong> Individual delete uses onclick confirmDelete(), bulk delete uses checkbox selection</p>";

echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li><a href='../admin/contacts.php' target='_blank'>Test the actual contacts page</a></li>";
echo "<li>Try creating some contacts and deleting them individually</li>";
echo "<li>Try selecting multiple contacts and using bulk delete</li>";
echo "<li>Check for any JavaScript console errors</li>";
echo "</ol>";

echo "<p><a href='test_delete_functionality.php'>Go to Interactive Test Page</a></p>";
?>
