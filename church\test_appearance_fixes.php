<?php
// Test Appearance and Theming Fixes
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Appearance and Theming Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .warn { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>";

echo "<h1>Appearance and Theming Fixes Test</h1>";
echo "<p>Testing all 5 critical appearance and theming issues...</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "<div class='test-result info'>🔄 Running: $testName</div>";
    
    try {
        $result = $testFunction();
        if ($result['status'] === 'PASS') {
            $passedTests++;
            echo "<div class='test-result pass'>✅ $testName: {$result['message']}</div>";
        } elseif ($result['status'] === 'WARN') {
            echo "<div class='test-result warn'>⚠️ $testName: {$result['message']}</div>";
        } else {
            echo "<div class='test-result fail'>❌ $testName: {$result['message']}</div>";
        }
        
        $testResults[$testName] = $result;
        
    } catch (Exception $e) {
        echo "<div class='test-result fail'>❌ $testName: Error - {$e->getMessage()}</div>";
        $testResults[$testName] = ['status' => 'FAIL', 'message' => 'Exception: ' . $e->getMessage()];
    }
}

// Test 1: CSS Generation and Cascade
echo "<div class='test-section'><h2>1. CSS Generation and Cascade</h2>";

runTest("Custom CSS File Generation", function() {
    $cssFile = __DIR__ . '/admin/css/custom-theme.css';
    
    // Trigger CSS generation
    include_once __DIR__ . '/admin/appearance_settings.php';
    
    if (file_exists($cssFile)) {
        $content = file_get_contents($cssFile);
        if (strpos($content, ':root') !== false && strpos($content, '--sidebar-bg-color') !== false) {
            return ['status' => 'PASS', 'message' => 'CSS file generated with proper variables'];
        } else {
            return ['status' => 'FAIL', 'message' => 'CSS file missing required variables'];
        }
    } else {
        return ['status' => 'FAIL', 'message' => 'CSS file not generated'];
    }
});

runTest("CSS Variables Definition", function() {
    $cssFile = __DIR__ . '/admin/css/custom-theme.css';
    if (file_exists($cssFile)) {
        $content = file_get_contents($cssFile);
        $requiredVars = [
            '--sidebar-bg-color',
            '--sidebar-text-color', 
            '--sidebar-hover-color',
            '--sidebar-width',
            '--content-spacing',
            '--logo-height'
        ];
        
        $missingVars = [];
        foreach ($requiredVars as $var) {
            if (strpos($content, $var) === false) {
                $missingVars[] = $var;
            }
        }
        
        if (empty($missingVars)) {
            return ['status' => 'PASS', 'message' => 'All required CSS variables defined'];
        } else {
            return ['status' => 'FAIL', 'message' => 'Missing variables: ' . implode(', ', $missingVars)];
        }
    } else {
        return ['status' => 'FAIL', 'message' => 'CSS file not found'];
    }
});

echo "</div>";

// Test 2: Sidebar Theme Application
echo "<div class='test-section'><h2>2. Sidebar Theme Application</h2>";

runTest("Sidebar CSS Classes", function() {
    $sidebarFile = __DIR__ . '/admin/includes/sidebar.php';
    $content = file_get_contents($sidebarFile);
    
    if (strpos($content, 'themed-sidebar') !== false) {
        return ['status' => 'PASS', 'message' => 'Sidebar has theme classes'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Sidebar missing theme classes'];
    }
});

runTest("Sidebar CSS Variables Usage", function() {
    $cssFile = __DIR__ . '/admin/css/admin-style.css';
    $content = file_get_contents($cssFile);
    
    if (strpos($content, 'var(--sidebar-bg-color') !== false && 
        strpos($content, 'var(--sidebar-text-color') !== false) {
        return ['status' => 'PASS', 'message' => 'Sidebar CSS uses theme variables'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Sidebar CSS not using theme variables'];
    }
});

echo "</div>";

// Test 3: Proper Spacing
echo "<div class='test-section'><h2>3. Proper Spacing Between Sidebar and Content</h2>";

runTest("Content Spacing Variable", function() {
    $cssFile = __DIR__ . '/admin/css/custom-theme.css';
    if (file_exists($cssFile)) {
        $content = file_get_contents($cssFile);
        if (strpos($content, '--content-spacing') !== false) {
            return ['status' => 'PASS', 'message' => 'Content spacing variable defined'];
        } else {
            return ['status' => 'FAIL', 'message' => 'Content spacing variable missing'];
        }
    } else {
        return ['status' => 'FAIL', 'message' => 'CSS file not found'];
    }
});

runTest("Main Content Margin Calculation", function() {
    $cssFile = __DIR__ . '/admin/css/custom-theme.css';
    if (file_exists($cssFile)) {
        $content = file_get_contents($cssFile);
        if (strpos($content, 'calc(var(--sidebar-width) + var(--content-spacing))') !== false) {
            return ['status' => 'PASS', 'message' => 'Main content uses proper spacing calculation'];
        } else {
            return ['status' => 'FAIL', 'message' => 'Main content spacing calculation missing'];
        }
    } else {
        return ['status' => 'FAIL', 'message' => 'CSS file not found'];
    }
});

echo "</div>";

// Test 4: Logo Display Implementation
echo "<div class='test-section'><h2>4. Logo Display Implementation</h2>";

runTest("Logo Container in Sidebar", function() {
    $sidebarFile = __DIR__ . '/admin/includes/sidebar.php';
    $content = file_get_contents($sidebarFile);
    
    if (strpos($content, 'logo-container') !== false && 
        strpos($content, 'sidebar-logo') !== false) {
        return ['status' => 'PASS', 'message' => 'Logo container and classes implemented'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Logo container missing'];
    }
});

runTest("Logo Height Setting", function() {
    $sidebarFile = __DIR__ . '/admin/includes/sidebar.php';
    $content = file_get_contents($sidebarFile);
    
    if (strpos($content, 'get_site_setting(\'logo_height\'') !== false) {
        return ['status' => 'PASS', 'message' => 'Logo height setting implemented'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Logo height setting missing'];
    }
});

runTest("Logo CSS Styling", function() {
    $cssFile = __DIR__ . '/admin/css/admin-style.css';
    $content = file_get_contents($cssFile);
    
    if (strpos($content, '.sidebar-logo') !== false && 
        strpos($content, 'var(--logo-height') !== false) {
        return ['status' => 'PASS', 'message' => 'Logo CSS styling with variables'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Logo CSS styling missing'];
    }
});

echo "</div>";

// Test 5: Favicon Support
echo "<div class='test-section'><h2>5. Favicon Support</h2>";

runTest("Favicon Meta Tags in Header", function() {
    $headerFile = __DIR__ . '/admin/includes/header.php';
    $content = file_get_contents($headerFile);
    
    if (strpos($content, 'favicon') !== false && 
        strpos($content, 'get_site_setting(\'favicon_url\'') !== false) {
        return ['status' => 'PASS', 'message' => 'Favicon meta tags implemented'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Favicon meta tags missing'];
    }
});

runTest("Favicon Setting in Form", function() {
    $appearanceFile = __DIR__ . '/admin/appearance_settings.php';
    $content = file_get_contents($appearanceFile);
    
    if (strpos($content, 'favicon_url') !== false) {
        return ['status' => 'PASS', 'message' => 'Favicon setting in appearance form'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Favicon setting missing from form'];
    }
});

echo "</div>";

// Test 6: Form Fields and Settings
echo "<div class='test-section'><h2>6. Appearance Settings Form</h2>";

runTest("Sidebar Color Fields", function() {
    $appearanceFile = __DIR__ . '/admin/appearance_settings.php';
    $content = file_get_contents($appearanceFile);
    
    $requiredFields = ['sidebar_bg_color', 'sidebar_text_color', 'sidebar_hover_color'];
    $missingFields = [];
    
    foreach ($requiredFields as $field) {
        if (strpos($content, $field) === false) {
            $missingFields[] = $field;
        }
    }
    
    if (empty($missingFields)) {
        return ['status' => 'PASS', 'message' => 'All sidebar color fields present'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Missing fields: ' . implode(', ', $missingFields)];
    }
});

runTest("Logo and Branding Fields", function() {
    $appearanceFile = __DIR__ . '/admin/appearance_settings.php';
    $content = file_get_contents($appearanceFile);
    
    $requiredFields = ['logo_url', 'favicon_url', 'logo_height', 'show_logo_text'];
    $missingFields = [];
    
    foreach ($requiredFields as $field) {
        if (strpos($content, $field) === false) {
            $missingFields[] = $field;
        }
    }
    
    if (empty($missingFields)) {
        return ['status' => 'PASS', 'message' => 'All logo and branding fields present'];
    } else {
        return ['status' => 'FAIL', 'message' => 'Missing fields: ' . implode(', ', $missingFields)];
    }
});

echo "</div>";

// Summary
$successRate = ($passedTests / $totalTests) * 100;

echo "<div class='test-section'><h2>Test Summary</h2>";
echo "<table>";
echo "<tr><th>Metric</th><th>Value</th></tr>";
echo "<tr><td>Total Tests</td><td>$totalTests</td></tr>";
echo "<tr><td>Passed</td><td>$passedTests</td></tr>";
echo "<tr><td>Failed</td><td>" . ($totalTests - $passedTests) . "</td></tr>";
echo "<tr><td>Success Rate</td><td>" . round($successRate, 1) . "%</td></tr>";
echo "</table>";

if ($successRate >= 90) {
    echo "<div class='test-result pass'><strong>🎉 Excellent!</strong> All appearance and theming fixes are working correctly.</div>";
} elseif ($successRate >= 75) {
    echo "<div class='test-result warn'><strong>⚠️ Good</strong> Most fixes are working with minor issues to address.</div>";
} else {
    echo "<div class='test-result fail'><strong>❌ Needs Work</strong> Several critical issues need to be resolved.</div>";
}

echo "</div>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>Test the appearance settings page in a browser</li>";
echo "<li>Try changing colors and verify they apply across all admin pages</li>";
echo "<li>Upload a logo and favicon to test the branding features</li>";
echo "<li>Adjust spacing and sidebar width to test responsiveness</li>";
echo "<li>Check that changes persist after browser refresh</li>";
echo "</ul>";

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
