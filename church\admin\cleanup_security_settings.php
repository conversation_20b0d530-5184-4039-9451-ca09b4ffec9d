<?php
/**
 * Security Settings Cleanup Script
 * 
 * This script migrates security settings from the unified settings table
 * to the dedicated security_settings table to eliminate duplication.
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Security Settings Cleanup</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h5 class='mb-0'><i class='bi bi-shield-lock'></i> Security Settings Cleanup</h5>
                </div>
                <div class='card-body'>
";

try {
    // Check if security_settings table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'security_settings'");
    $stmt->execute();
    $securityTableExists = $stmt->rowCount() > 0;
    
    if (!$securityTableExists) {
        echo "<div class='alert alert-info'>Creating security_settings table...</div>";
        
        // Create security_settings table
        $conn->exec("
            CREATE TABLE security_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                password_min_length INT NOT NULL DEFAULT 8,
                password_require_uppercase TINYINT(1) NOT NULL DEFAULT 1,
                password_require_lowercase TINYINT(1) NOT NULL DEFAULT 1,
                password_require_number TINYINT(1) NOT NULL DEFAULT 1,
                password_require_special TINYINT(1) NOT NULL DEFAULT 1,
                password_expiry_days INT NOT NULL DEFAULT 90,
                login_max_attempts INT NOT NULL DEFAULT 5,
                login_lockout_minutes INT NOT NULL DEFAULT 30,
                session_timeout_minutes INT NOT NULL DEFAULT 30,
                require_2fa_for_admins TINYINT(1) NOT NULL DEFAULT 0,
                audit_log_retention_days INT NOT NULL DEFAULT 90,
                csrf_token_expiry_minutes INT NOT NULL DEFAULT 60,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        echo "<div class='alert alert-success'>✓ Security settings table created successfully.</div>";
    }
    
    // Check for existing security settings in the main settings table
    $stmt = $conn->prepare("SELECT * FROM settings WHERE setting_key IN (
        'enable_2fa', 'password_min_length', 'password_require_uppercase', 
        'password_require_lowercase', 'password_require_numbers', 'password_require_symbols',
        'login_attempts_limit', 'lockout_duration', 'enable_login_notifications', 'enable_audit_log'
    )");
    $stmt->execute();
    $oldSecuritySettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($oldSecuritySettings)) {
        echo "<div class='alert alert-warning'>Found " . count($oldSecuritySettings) . " duplicate security settings in main settings table.</div>";
        
        // Map old settings to new security settings format
        $securityData = [
            'password_min_length' => 8,
            'password_require_uppercase' => 1,
            'password_require_lowercase' => 1,
            'password_require_number' => 1,
            'password_require_special' => 1,
            'password_expiry_days' => 90,
            'login_max_attempts' => 5,
            'login_lockout_minutes' => 30,
            'session_timeout_minutes' => 30,
            'require_2fa_for_admins' => 0,
            'audit_log_retention_days' => 90,
            'csrf_token_expiry_minutes' => 60
        ];
        
        // Map old setting keys to new ones
        $settingMap = [
            'password_min_length' => 'password_min_length',
            'password_require_uppercase' => 'password_require_uppercase',
            'password_require_lowercase' => 'password_require_lowercase',
            'password_require_numbers' => 'password_require_number',
            'password_require_symbols' => 'password_require_special',
            'login_attempts_limit' => 'login_max_attempts',
            'lockout_duration' => 'login_lockout_minutes',
            'enable_2fa' => 'require_2fa_for_admins',
            'enable_audit_log' => 'audit_log_retention_days' // Convert boolean to days
        ];
        
        foreach ($oldSecuritySettings as $setting) {
            $oldKey = $setting['setting_key'];
            if (isset($settingMap[$oldKey])) {
                $newKey = $settingMap[$oldKey];
                $value = $setting['setting_value'];
                
                // Special handling for audit log (convert boolean to days)
                if ($oldKey === 'enable_audit_log') {
                    $securityData['audit_log_retention_days'] = $value === '1' ? 90 : 0;
                } else {
                    $securityData[$newKey] = $value;
                }
            }
        }
        
        // Check if security settings already exist
        $stmt = $conn->prepare("SELECT COUNT(*) FROM security_settings");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            // Update existing security settings
            $sql = "UPDATE security_settings SET 
                password_min_length = :password_min_length,
                password_require_uppercase = :password_require_uppercase,
                password_require_lowercase = :password_require_lowercase,
                password_require_number = :password_require_number,
                password_require_special = :password_require_special,
                password_expiry_days = :password_expiry_days,
                login_max_attempts = :login_max_attempts,
                login_lockout_minutes = :login_lockout_minutes,
                session_timeout_minutes = :session_timeout_minutes,
                require_2fa_for_admins = :require_2fa_for_admins,
                audit_log_retention_days = :audit_log_retention_days,
                csrf_token_expiry_minutes = :csrf_token_expiry_minutes
            ";
        } else {
            // Insert new security settings
            $sql = "INSERT INTO security_settings (
                password_min_length, password_require_uppercase, password_require_lowercase,
                password_require_number, password_require_special, password_expiry_days,
                login_max_attempts, login_lockout_minutes, session_timeout_minutes,
                require_2fa_for_admins, audit_log_retention_days, csrf_token_expiry_minutes
            ) VALUES (
                :password_min_length, :password_require_uppercase, :password_require_lowercase,
                :password_require_number, :password_require_special, :password_expiry_days,
                :login_max_attempts, :login_lockout_minutes, :session_timeout_minutes,
                :require_2fa_for_admins, :audit_log_retention_days, :csrf_token_expiry_minutes
            )";
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($securityData);
        
        echo "<div class='alert alert-success'>✓ Security settings migrated to dedicated table.</div>";
        
        // Remove duplicate settings from main settings table
        $stmt = $conn->prepare("DELETE FROM settings WHERE setting_key IN (
            'enable_2fa', 'password_min_length', 'password_require_uppercase', 
            'password_require_lowercase', 'password_require_numbers', 'password_require_symbols',
            'login_attempts_limit', 'lockout_duration', 'enable_login_notifications', 'enable_audit_log'
        )");
        $stmt->execute();
        
        echo "<div class='alert alert-success'>✓ Duplicate security settings removed from main settings table.</div>";
        echo "<div class='alert alert-info'>Migrated " . count($oldSecuritySettings) . " security settings.</div>";
    } else {
        echo "<div class='alert alert-info'>No duplicate security settings found in main settings table.</div>";
    }
    
    echo "<div class='alert alert-success mt-4'>
        <h6>✅ Security Settings Cleanup Complete!</h6>
        <p class='mb-0'>
            • Security settings are now managed exclusively in <a href='security_settings.php' class='alert-link'>Security Settings</a><br>
            • General system settings remain in <a href='settings.php' class='alert-link'>Settings</a><br>
            • No more duplication between the two systems
        </p>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='settings.php' class='btn btn-primary'>Go to Settings</a>
                        <a href='security_settings.php' class='btn btn-outline-primary'>Go to Security Settings</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
