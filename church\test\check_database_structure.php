<?php
/**
 * Check database structure for contact-related tables
 */

require_once '../config.php';

$conn = $pdo;

echo "<h1>Database Structure Analysis</h1>";

// Tables to check
$tables = [
    'contacts',
    'contact_groups', 
    'contact_group_members',
    'contact_email_logs',
    'email_tracking',
    'email_logs'
];

foreach ($tables as $table) {
    echo "<h2>Table: $table</h2>";
    
    try {
        // Check if table exists
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            echo "<p style='color: orange;'>⚠️ Table '$table' does not exist</p>";
            continue;
        }
        
        echo "<p style='color: green;'>✅ Table exists</p>";
        
        // Get table structure
        $stmt = $conn->query("DESCRIBE $table");
        $columns = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>" . $column['Field'] . "</strong></td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Get sample data count
        $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetchColumn();
        echo "<p>Records in table: <strong>$count</strong></p>";
        
        // Show foreign key relationships
        $stmt = $conn->query("
            SELECT 
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = '$table' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $foreign_keys = $stmt->fetchAll();
        
        if (!empty($foreign_keys)) {
            echo "<h4>Foreign Keys:</h4>";
            echo "<ul>";
            foreach ($foreign_keys as $fk) {
                echo "<li><strong>{$fk['COLUMN_NAME']}</strong> → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</li>";
            }
            echo "</ul>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ Error checking table '$table': " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Check for any tables that might reference contacts
echo "<h2>Tables that reference 'contacts'</h2>";
try {
    $stmt = $conn->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME = 'contacts'
    ");
    $referencing_tables = $stmt->fetchAll();
    
    if (!empty($referencing_tables)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Table</th><th>Column</th><th>References</th></tr>";
        foreach ($referencing_tables as $ref) {
            echo "<tr>";
            echo "<td>{$ref['TABLE_NAME']}</td>";
            echo "<td><strong>{$ref['COLUMN_NAME']}</strong></td>";
            echo "<td>{$ref['REFERENCED_TABLE_NAME']}.{$ref['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No tables found that reference the contacts table via foreign keys.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error checking foreign key references: " . $e->getMessage() . "</p>";
}

echo "<h2>Recommended Delete Order</h2>";
echo "<p>Based on the foreign key relationships found above, contacts should be deleted in this order:</p>";
echo "<ol>";
echo "<li>Delete from tables that reference contacts (child tables)</li>";
echo "<li>Delete from contacts table (parent table)</li>";
echo "</ol>";
?>
