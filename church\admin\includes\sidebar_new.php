<?php
// Get the current page filename to highlight the active menu item
$current_page = basename($_SERVER['PHP_SELF']);

// Helper function to build admin URL consistently
function admin_url_for($page) {
    if (defined('ADMIN_URL')) {
        return ADMIN_URL . '/' . $page;
    }
    return $page; // Fallback for pages in the same directory
}

// Helper function to check if current page is active
function is_active($page_name) {
    global $current_page;
    return ($current_page == $page_name) ? 'class="active"' : '';
}

// Helper function to check if any page in array is active (for collapsible sections)
function is_section_active($pages) {
    global $current_page;
    return in_array($current_page, $pages) ? 'show' : '';
}
?>

<!-- Sidebar -->
<div class="col-auto sidebar" id="sidebar">
    <div class="d-flex justify-content-between align-items-center mb-3 ps-2 pe-2">
        <h5 class="navbar-brand mb-0"><span class="navbar-brand-text"><?php echo get_admin_title(); ?></span></h5>
        <button class="d-md-none btn btn-sm btn-outline-light" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a <?php echo is_active('dashboard.php'); ?> href="<?php echo admin_url_for('dashboard.php'); ?>" title="Dashboard">
                    <i class="bi bi-speedometer2"></i> <span class="menu-text">Dashboard</span>
                </a>
            </li>
            
            <!-- Member Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#memberManagement" data-bs-toggle="collapse" aria-expanded="<?php echo is_section_active(['members.php', 'add_member.php']) ? 'true' : 'false'; ?>" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span><?php echo get_member_term(); ?> Management</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse <?php echo is_section_active(['members.php', 'add_member.php']); ?>" id="memberManagement">
                <li class="nav-item">
                    <a <?php echo is_active('members.php'); ?> href="<?php echo admin_url_for('members.php'); ?>" title="<?php echo get_member_term(true); ?>">
                        <i class="bi bi-people"></i> <span class="menu-text"><?php echo get_member_term(true); ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('add_member.php'); ?> href="<?php echo admin_url_for('add_member.php'); ?>" title="Add <?php echo get_member_term(); ?>">
                        <i class="bi bi-person-plus"></i> <span class="menu-text">Add <?php echo get_member_term(); ?></span>
                    </a>
                </li>
            </div>
            
            <!-- Email Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#emailManagement" data-bs-toggle="collapse" aria-expanded="<?php echo is_section_active(['bulk_email.php', 'email_scheduler.php', 'contacts.php', 'contact_groups.php', 'birthday.php', 'email_templates.php', 'email_tracking.php', 'email_analytics.php']) ? 'true' : 'false'; ?>" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Email Management</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse <?php echo is_section_active(['bulk_email.php', 'email_scheduler.php', 'contacts.php', 'contact_groups.php', 'birthday.php', 'email_templates.php', 'email_tracking.php', 'email_analytics.php']); ?>" id="emailManagement">
                <li class="nav-item">
                    <a <?php echo is_active('bulk_email.php'); ?> href="<?php echo admin_url_for('bulk_email.php'); ?>" title="Bulk Email">
                        <i class="bi bi-envelope-fill"></i> <span class="menu-text">Bulk Email</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('email_scheduler.php'); ?> href="<?php echo admin_url_for('email_scheduler.php'); ?>" title="Email Scheduler">
                        <i class="bi bi-calendar-event"></i> <span class="menu-text">Email Scheduler</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('email_templates.php'); ?> href="<?php echo admin_url_for('email_templates.php'); ?>" title="Email Templates">
                        <i class="bi bi-file-earmark-text"></i> <span class="menu-text">Email Templates</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('contacts.php'); ?> href="<?php echo admin_url_for('contacts.php'); ?>" title="Contact Management">
                        <i class="bi bi-person-lines-fill"></i> <span class="menu-text">Contacts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('contact_groups.php'); ?> href="<?php echo admin_url_for('contact_groups.php'); ?>" title="Contact Groups">
                        <i class="bi bi-folder"></i> <span class="menu-text">Contact Groups</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('birthday.php'); ?> href="<?php echo admin_url_for('birthday.php'); ?>" title="Birthday Messages">
                        <i class="bi bi-gift"></i> <span class="menu-text">Birthday Messages</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('email_tracking.php'); ?> href="<?php echo admin_url_for('email_tracking.php'); ?>" title="Email Tracking">
                        <i class="bi bi-bar-chart"></i> <span class="menu-text">Email Tracking</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('email_analytics.php'); ?> href="<?php echo admin_url_for('email_analytics.php'); ?>" title="Email Analytics">
                        <i class="bi bi-graph-up"></i> <span class="menu-text">Email Analytics</span>
                    </a>
                </li>
            </div>
            
            <!-- Financial Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#financialManagement" data-bs-toggle="collapse" aria-expanded="<?php echo is_section_active(['donations.php', 'payment_tables.php', 'check_payment_sdks.php']) ? 'true' : 'false'; ?>" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Financial Management</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse <?php echo is_section_active(['donations.php', 'payment_tables.php', 'check_payment_sdks.php']); ?>" id="financialManagement">
                <li class="nav-item">
                    <a <?php echo is_active('donations.php'); ?> href="<?php echo admin_url_for('donations.php'); ?>" title="Manage Donations">
                        <i class="bi bi-cash-coin"></i> <span class="menu-text">Donations</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('payment_tables.php'); ?> href="<?php echo admin_url_for('payment_tables.php'); ?>" title="Payment Tables Setup">
                        <i class="bi bi-table"></i> <span class="menu-text">Payment Tables</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('check_payment_sdks.php'); ?> href="<?php echo admin_url_for('check_payment_sdks.php'); ?>" title="Check Payment SDKs">
                        <i class="bi bi-check-circle"></i> <span class="menu-text">Check Payment SDKs</span>
                    </a>
                </li>
            </div>
            
            <!-- System Tools -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#systemTools" data-bs-toggle="collapse" aria-expanded="<?php echo is_section_active(['debug_placeholders.php', 'create_missing_tables.php', 'about_shortcodes.php']) ? 'true' : 'false'; ?>" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>System Tools</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse <?php echo is_section_active(['debug_placeholders.php', 'create_missing_tables.php', 'about_shortcodes.php']); ?>" id="systemTools">
                <li class="nav-item">
                    <a <?php echo is_active('debug_placeholders.php'); ?> href="<?php echo admin_url_for('debug_placeholders.php'); ?>" title="Debug Email Placeholders">
                        <i class="bi bi-bug"></i> <span class="menu-text">Debug Placeholders</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('create_missing_tables.php'); ?> href="<?php echo admin_url_for('create_missing_tables.php'); ?>" title="Create Missing Tables">
                        <i class="bi bi-database-add"></i> <span class="menu-text">Create Missing Tables</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('about_shortcodes.php'); ?> href="<?php echo admin_url_for('about_shortcodes.php'); ?>" title="About & Shortcodes">
                        <i class="bi bi-info-circle"></i> <span class="menu-text">About & Shortcodes</span>
                    </a>
                </li>
            </div>
            
            <!-- Account & Settings -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#account" data-bs-toggle="collapse" aria-expanded="<?php echo is_section_active(['settings.php', 'appearance_settings.php', 'logo_management.php', 'profile.php', 'security_settings.php', 'security_audit.php']) ? 'true' : 'false'; ?>" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Account & Settings</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse <?php echo is_section_active(['settings.php', 'appearance_settings.php', 'logo_management.php', 'profile.php', 'security_settings.php', 'security_audit.php']); ?>" id="account">
                <li class="nav-item">
                    <a <?php echo is_active('settings.php'); ?> href="<?php echo admin_url_for('settings.php'); ?>" title="Settings">
                        <i class="bi bi-gear"></i> <span class="menu-text">Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('appearance_settings.php'); ?> href="<?php echo admin_url_for('appearance_settings.php'); ?>" title="Appearance Settings">
                        <i class="bi bi-palette"></i> <span class="menu-text">Appearance</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('logo_management.php'); ?> href="<?php echo admin_url_for('logo_management.php'); ?>" title="Logo Management">
                        <i class="bi bi-image"></i> <span class="menu-text">Logo Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('profile.php'); ?> href="<?php echo admin_url_for('profile.php'); ?>" title="My Profile">
                        <i class="bi bi-person-circle"></i> <span class="menu-text">My Profile</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('security_settings.php'); ?> href="<?php echo admin_url_for('security_settings.php'); ?>" title="Security Settings">
                        <i class="bi bi-shield-check"></i> <span class="menu-text">Security Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo is_active('security_audit.php'); ?> href="<?php echo admin_url_for('security_audit.php'); ?>" title="Security Audit">
                        <i class="bi bi-shield-lock"></i> <span class="menu-text">Security Audit</span>
                    </a>
                </li>
            </div>
            
            <!-- Logout -->
            <li class="nav-item mt-3">
                <a href="<?php echo admin_url_for('logout.php'); ?>" title="Logout" class="text-danger">
                    <i class="bi bi-box-arrow-right"></i> <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const collapseToggle = document.getElementById('sidebarCollapseToggle');
    const mainContent = document.querySelector('.main-content');
    const isMobile = window.innerWidth <= 768;
    
    // Mobile sidebar toggle
    if(sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('sidebar-collapsed');
        });
    }
    
    // Desktop sidebar collapse toggle
    if(collapseToggle) {
        collapseToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('sidebar-collapsed');
            
            // Store preference in localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('sidebar-collapsed'));
        });
    }
    
    // Restore sidebar state from localStorage
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
    if (sidebarCollapsed === 'true' && !isMobile) {
        sidebar.classList.add('sidebar-collapsed');
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        const newIsMobile = window.innerWidth <= 768;
        if (newIsMobile !== isMobile) {
            // Remove collapsed class on mobile
            if (newIsMobile) {
                sidebar.classList.remove('sidebar-collapsed');
            }
        }
    });
    
    // Auto-collapse sidebar on mobile when clicking outside
    if (isMobile) {
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                sidebar.classList.add('sidebar-collapsed');
            }
        });
    }
});
</script>
