#!/usr/bin/env python3
"""
Focused Test Suite for Church Campaign Management System
Tests only the files that actually exist and are critical for functionality
"""

import requests
import json
import time
from urllib.parse import urljoin

class FocusedChurchTester:
    def __init__(self, base_url="http://localhost/campaign/church"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        self.errors = []
        
    def log_test(self, test_name, status, message=""):
        """Log test results"""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        status_symbol = "✓" if status == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {message}")
        
        if status == "FAIL":
            self.errors.append(result)
    
    def test_page_accessibility(self, path, expected_status=200, test_name=None):
        """Test if a page is accessible"""
        if test_name is None:
            test_name = f"Page Access: {path}"

        try:
            # Fix URL joining - remove leading slash to make it relative
            clean_path = path.lstrip('/')
            url = urljoin(self.base_url + '/', clean_path)
            response = self.session.get(url, timeout=10)
            
            if response.status_code == expected_status:
                self.log_test(test_name, "PASS", f"Status: {response.status_code}")
                return response
            elif response.status_code == 302:
                self.log_test(test_name, "PASS", f"Redirect (302) - Expected behavior")
                return response
            else:
                self.log_test(test_name, "FAIL", f"Expected {expected_status}, got {response.status_code}")
                return None
                
        except Exception as e:
            self.log_test(test_name, "FAIL", f"Error: {str(e)}")
            return None
    
    def run_focused_tests(self):
        """Run focused tests on existing files"""
        print("Starting Focused Church App Test Suite")
        print("=" * 50)
        
        # Test 1: Core Application Files
        print("\n=== Testing Core Application Files ===")
        
        core_files = [
            "/index.php",
            "/config.php",
            "/register.php",
            "/events.php",
            "/event_detail.php",
            "/donate.php"
        ]
        
        for file in core_files:
            self.test_page_accessibility(file)
        
        # Test 2: Admin Panel Files (that exist)
        print("\n=== Testing Admin Panel Files ===")
        
        admin_files = [
            "/admin/login.php",
            "/admin/dashboard.php",
            "/admin/members.php",
            "/admin/add_member.php",
            "/admin/email_templates.php",
            "/admin/birthday.php",
            "/admin/bulk_email.php",
            "/admin/settings.php",
            "/admin/appearance_settings.php",
            "/admin/events.php",
            "/admin/donations.php",
            "/admin/contacts.php",
            "/admin/whatsapp_templates.php"
        ]
        
        for file in admin_files:
            self.test_page_accessibility(file)
        
        # Test 3: User System Files
        print("\n=== Testing User System Files ===")
        
        user_files = [
            "/user/login.php",
            "/user/register.php",
            "/user/dashboard.php",
            "/user/profile.php",
            "/user/events.php"
        ]
        
        for file in user_files:
            self.test_page_accessibility(file)
        
        # Test 4: API and AJAX Files
        print("\n=== Testing API and AJAX Files ===")
        
        api_files = [
            "/api/birthdays.php",
            "/ajax/get_recipient_details.php",
            "/ajax/send_single_email.php"
        ]
        
        for file in api_files:
            self.test_page_accessibility(file)
        
        # Test 5: Our New Test Files
        print("\n=== Testing Our New Test Files ===")
        
        test_files = [
            "/quick_validation.php",
            "/comprehensive_template_analysis.php",
            "/production_readiness_check.php",
            "/comprehensive_system_test.php",
            "/test_shortcodes.php",
            "/test_birthday_fixes.php"
        ]
        
        for file in test_files:
            self.test_page_accessibility(file)
        
        # Test 6: Database and System Files
        print("\n=== Testing Database and System Files ===")
        
        system_files = [
            "/check_database.php",
            "/check_members.php",
            "/check_smtp.php",
            "/phpinfo.php"
        ]
        
        for file in system_files:
            self.test_page_accessibility(file)
        
        # Test 7: Cron Jobs
        print("\n=== Testing Cron Jobs ===")
        
        cron_files = [
            "/cron/birthday_reminders.php",
            "/cron/process_birthday_reminders.php",
            "/cron/event_reminders.php"
        ]
        
        for file in cron_files:
            self.test_page_accessibility(file)
        
        # Generate summary
        self.generate_summary()
    
    def generate_summary(self):
        """Generate test summary"""
        print("\n" + "=" * 50)
        print("FOCUSED TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.errors:
            print(f"\n{len(self.errors)} FAILED TESTS:")
            for error in self.errors:
                print(f"  ✗ {error['test']}: {error['message']}")
        
        # Determine overall status
        success_rate = (passed_tests/total_tests)*100
        if success_rate >= 80:
            print(f"\n🎉 EXCELLENT! System is {success_rate:.1f}% functional")
        elif success_rate >= 60:
            print(f"\n👍 GOOD! System is {success_rate:.1f}% functional with minor issues")
        else:
            print(f"\n⚠️ NEEDS ATTENTION! System is only {success_rate:.1f}% functional")
        
        # Save results
        try:
            with open('focused_test_report.json', 'w') as f:
                json.dump({
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'success_rate': success_rate
                    },
                    'results': self.test_results
                }, f, indent=2)
            print(f"\nDetailed report saved to: focused_test_report.json")
        except Exception as e:
            print(f"Could not save report: {e}")

if __name__ == "__main__":
    tester = FocusedChurchTester()
    tester.run_focused_tests()
