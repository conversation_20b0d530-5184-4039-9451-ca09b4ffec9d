<?php
/**
 * Settings Database Cleanup Script
 * This script consolidates all settings into the main 'settings' table
 * and removes duplicates from separate tables like 'email_settings'
 */

require_once '../config.php';

echo "<h2>Settings Database Cleanup</h2>\n";
echo "<pre>\n";

try {
    $pdo->beginTransaction();
    
    // Step 1: Check if email_settings table exists and migrate data
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_settings'");
    if ($stmt->rowCount() > 0) {
        echo "1. Found email_settings table, migrating data...\n";
        
        // Get all email settings
        $emailSettings = $pdo->query("SELECT setting_key, setting_value FROM email_settings")->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($emailSettings as $setting) {
            // Insert or update in main settings table
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                                 ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $stmt->execute([$setting['setting_key'], $setting['setting_value']]);
            echo "   Migrated: {$setting['setting_key']}\n";
        }
        
        // Drop the email_settings table
        $pdo->exec("DROP TABLE email_settings");
        echo "   Dropped email_settings table\n";
    } else {
        echo "1. No email_settings table found, skipping migration\n";
    }
    
    // Step 2: Remove duplicate social media settings (old vs new naming)
    echo "\n2. Cleaning up duplicate social media settings...\n";
    $socialMappings = [
        'social_facebook' => 'facebook_url',
        'social_twitter' => 'twitter_url',
        'social_instagram' => 'instagram_url',
        'social_youtube' => 'youtube_url'
    ];
    
    foreach ($socialMappings as $oldKey => $newKey) {
        // Check if old key exists
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$oldKey]);
        $oldValue = $stmt->fetchColumn();
        
        if ($oldValue) {
            // Update new key with old value if new key doesn't exist or is empty
            $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
            $stmt->execute([$newKey]);
            $newValue = $stmt->fetchColumn();
            
            if (!$newValue) {
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                                     ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
                $stmt->execute([$newKey, $oldValue]);
                echo "   Migrated $oldKey -> $newKey: $oldValue\n";
            }
            
            // Remove old key
            $stmt = $pdo->prepare("DELETE FROM settings WHERE setting_key = ?");
            $stmt->execute([$oldKey]);
            echo "   Removed duplicate: $oldKey\n";
        }
    }
    
    // Step 3: Remove duplicate contact settings
    echo "\n3. Cleaning up duplicate contact settings...\n";
    $contactMappings = [
        'church_address' => 'contact_address',
        'church_service_times' => 'office_hours'
    ];
    
    foreach ($contactMappings as $oldKey => $newKey) {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$oldKey]);
        $oldValue = $stmt->fetchColumn();
        
        if ($oldValue) {
            $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
            $stmt->execute([$newKey]);
            $newValue = $stmt->fetchColumn();
            
            if (!$newValue) {
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                                     ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
                $stmt->execute([$newKey, $oldValue]);
                echo "   Migrated $oldKey -> $newKey: $oldValue\n";
            }
            
            $stmt = $pdo->prepare("DELETE FROM settings WHERE setting_key = ?");
            $stmt->execute([$oldKey]);
            echo "   Removed duplicate: $oldKey\n";
        }
    }
    
    // Step 4: Remove email prefixed duplicates
    echo "\n4. Cleaning up email prefixed duplicates...\n";
    $emailPrefixedKeys = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 
                         'from_email', 'from_name', 'reply_to_email'];
    
    foreach ($emailPrefixedKeys as $key) {
        $prefixedKey = 'email_' . $key;
        
        // Check if prefixed version exists
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$prefixedKey]);
        $prefixedValue = $stmt->fetchColumn();
        
        if ($prefixedValue) {
            // Check if non-prefixed version exists
            $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $nonPrefixedValue = $stmt->fetchColumn();
            
            if (!$nonPrefixedValue) {
                // Move prefixed to non-prefixed
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                                     ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
                $stmt->execute([$key, $prefixedValue]);
                echo "   Migrated $prefixedKey -> $key: $prefixedValue\n";
            }
            
            // Remove prefixed version
            $stmt = $pdo->prepare("DELETE FROM settings WHERE setting_key = ?");
            $stmt->execute([$prefixedKey]);
            echo "   Removed duplicate: $prefixedKey\n";
        }
    }
    
    // Step 5: Set default values for missing settings
    echo "\n5. Setting default values for missing settings...\n";
    $defaultSettings = [
        'site_title' => 'Freedom Assembly Church',
        'admin_title' => 'Church Admin',
        'organization_type' => 'church',
        'member_term' => 'Member',
        'leader_term' => 'Pastor',
        'group_term' => 'Ministry',
        'event_term' => 'Service',
        'donation_term' => 'Offering',
        'timezone' => 'America/New_York',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i',
        'currency_symbol' => '$',
        'currency_code' => 'USD',
        'language' => 'en',
        'items_per_page' => '25',
        'session_timeout' => '3600',
        'max_upload_size' => '10',
        'backup_retention_days' => '30',
        'password_min_length' => '8',
        'login_attempts_limit' => '5',
        'lockout_duration' => '15',
        'birthday_notification_days' => '7',
        'event_reminder_days' => '3',
        'membership_expiry_days' => '30',
        'notification_frequency' => 'daily',
        'payment_gateway' => 'stripe',
        'smtp_port' => '587',
        'smtp_encryption' => 'tls'
    ];
    
    foreach ($defaultSettings as $key => $defaultValue) {
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $currentValue = $stmt->fetchColumn();
        
        if ($currentValue === false) {
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $defaultValue]);
            echo "   Set default: $key = $defaultValue\n";
        }
    }
    
    // Step 6: Show final settings count
    echo "\n6. Final verification...\n";
    $stmt = $pdo->query("SELECT COUNT(*) FROM settings");
    $totalSettings = $stmt->fetchColumn();
    echo "   Total settings in database: $totalSettings\n";
    
    $pdo->commit();
    echo "\n✅ Settings cleanup completed successfully!\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "\n❌ Error during cleanup: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
echo "<p><a href='settings.php'>Go to Unified Settings Page</a></p>\n";
?>
