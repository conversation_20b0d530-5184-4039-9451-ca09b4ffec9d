<?php
/**
 * Organization-Agnostic System Test Script
 * 
 * This script tests the organization-agnostic functionality by:
 * 1. Testing different organization types and their terminology
 * 2. Verifying dynamic placeholders work correctly
 * 3. Testing email template placeholder replacement
 * 4. Checking admin interface dynamic labels
 */

require_once '../config.php';

echo "<!DOCTYPE html>\n<html><head><title>Organization-Agnostic System Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>Organization-Agnostic System Test</h1>";

// Test 1: Current Organization Settings
echo "<div class='test-section'>";
echo "<h2>Test 1: Current Organization Settings</h2>";

$currentSettings = [
    'organization_type' => get_site_setting('organization_type', 'not set'),
    'organization_name' => get_site_setting('organization_name', 'not set'),
    'organization_mission' => get_site_setting('organization_mission', 'not set'),
    'organization_vision' => get_site_setting('organization_vision', 'not set'),
    'organization_values' => get_site_setting('organization_values', 'not set'),
    'member_term' => get_site_setting('member_term', 'not set'),
    'leader_term' => get_site_setting('leader_term', 'not set'),
    'group_term' => get_site_setting('group_term', 'not set'),
    'event_term' => get_site_setting('event_term', 'not set'),
    'donation_term' => get_site_setting('donation_term', 'not set'),
];

echo "<table>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
foreach ($currentSettings as $key => $value) {
    $status = ($value === 'not set') ? 'warning' : 'success';
    echo "<tr><td>$key</td><td class='$status'>$value</td></tr>";
}
echo "</table>";
echo "</div>";

// Test 2: Dynamic Function Tests
echo "<div class='test-section'>";
echo "<h2>Test 2: Dynamic Helper Functions</h2>";

$functions = [
    'get_organization_name()' => get_organization_name(),
    'get_organization_type()' => get_organization_type(),
    'get_site_title()' => get_site_title(),
    'get_admin_title()' => get_admin_title(),
    'get_member_term()' => get_member_term(),
    'get_member_term(true)' => get_member_term(true),
    'get_leader_term()' => get_leader_term(),
    'get_group_term()' => get_group_term(),
    'get_event_term()' => get_event_term(),
    'get_donation_term()' => get_donation_term(),
];

echo "<table>";
echo "<tr><th>Function</th><th>Result</th></tr>";
foreach ($functions as $func => $result) {
    echo "<tr><td>$func</td><td class='info'>$result</td></tr>";
}
echo "</table>";
echo "</div>";

// Test 3: Placeholder Replacement Test
echo "<div class='test-section'>";
echo "<h2>Test 3: Placeholder Replacement System</h2>";

$testContent = "Welcome to {organization_name}! We are a {organization_type} that serves our {member_term}s. Our {leader_term} leads our {group_term}s in various {event_term}s. Your {donation_term}s help us continue our mission.";

echo "<p><strong>Test Content:</strong></p>";
echo "<p style='background: #f5f5f5; padding: 10px;'>$testContent</p>";

// Test the placeholder replacement
$replacedContent = replace_placeholders($testContent, []);

echo "<p><strong>After Placeholder Replacement:</strong></p>";
echo "<p style='background: #e8f5e8; padding: 10px;'>$replacedContent</p>";
echo "</div>";

// Test 4: Email Template Placeholder Test
echo "<div class='test-section'>";
echo "<h2>Test 4: Email Template Placeholders</h2>";

try {
    $stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates LIMIT 3");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<p class='warning'>No email templates found in database.</p>";
    } else {
        foreach ($templates as $template) {
            echo "<h3>Template: " . htmlspecialchars($template['template_name']) . "</h3>";
            
            // Check for organization placeholders
            $orgPlaceholders = [
                '{organization_name}', '{organization_type}', '{member_term}', 
                '{leader_term}', '{group_term}', '{event_term}', '{donation_term}'
            ];
            
            $foundPlaceholders = [];
            foreach ($orgPlaceholders as $placeholder) {
                if (strpos($template['content'], $placeholder) !== false || 
                    strpos($template['subject'], $placeholder) !== false) {
                    $foundPlaceholders[] = $placeholder;
                }
            }
            
            if (!empty($foundPlaceholders)) {
                echo "<p class='success'>✓ Organization placeholders found: " . implode(', ', $foundPlaceholders) . "</p>";
            } else {
                echo "<p class='warning'>⚠ No organization placeholders found - may need migration</p>";
            }
            
            // Check for old hardcoded terms
            $oldTerms = ['Freedom Assembly Church', 'Church Team', 'church family', 'Pastor', 'Blessings,'];
            $foundOldTerms = [];
            foreach ($oldTerms as $term) {
                if (strpos($template['content'], $term) !== false || 
                    strpos($template['subject'], $term) !== false) {
                    $foundOldTerms[] = $term;
                }
            }
            
            if (!empty($foundOldTerms)) {
                echo "<p class='error'>✗ Old hardcoded terms found: " . implode(', ', $foundOldTerms) . "</p>";
            } else {
                echo "<p class='success'>✓ No old hardcoded terms found</p>";
            }
        }
    }
} catch (PDOException $e) {
    echo "<p class='error'>Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 5: Organization Type Scenarios
echo "<div class='test-section'>";
echo "<h2>Test 5: Organization Type Scenarios</h2>";

$scenarios = [
    'church' => [
        'organization_name' => 'Grace Community Church',
        'member_term' => 'Member',
        'leader_term' => 'Pastor',
        'group_term' => 'Ministry',
        'event_term' => 'Service',
        'donation_term' => 'Offering'
    ],
    'school' => [
        'organization_name' => 'Riverside Elementary School',
        'member_term' => 'Student',
        'leader_term' => 'Principal',
        'group_term' => 'Class',
        'event_term' => 'Assembly',
        'donation_term' => 'Fee'
    ],
    'business' => [
        'organization_name' => 'Tech Solutions Inc',
        'member_term' => 'Employee',
        'leader_term' => 'Manager',
        'group_term' => 'Department',
        'event_term' => 'Meeting',
        'donation_term' => 'Contribution'
    ]
];

foreach ($scenarios as $type => $settings) {
    echo "<h3>Scenario: " . ucfirst($type) . "</h3>";
    echo "<table>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    foreach ($settings as $key => $value) {
        echo "<tr><td>$key</td><td>$value</td></tr>";
    }
    echo "</table>";
    
    // Show how a welcome message would look
    $welcomeMessage = "Welcome to {organization_name}! As a new {member_term}, you'll be part of our community. Our {leader_term} and {group_term} leaders organize {event_term}s throughout the year.";
    $simulatedMessage = str_replace(
        ['{organization_name}', '{member_term}', '{leader_term}', '{group_term}', '{event_term}'],
        [$settings['organization_name'], $settings['member_term'], $settings['leader_term'], $settings['group_term'], $settings['event_term']],
        $welcomeMessage
    );
    echo "<p><strong>Sample Welcome Message:</strong></p>";
    echo "<p style='background: #f0f8ff; padding: 10px; font-style: italic;'>$simulatedMessage</p>";
}
echo "</div>";

// Test 6: Migration Recommendations
echo "<div class='test-section'>";
echo "<h2>Test 6: Migration Recommendations</h2>";

$recommendations = [];

// Check if organization settings are configured
if (get_site_setting('organization_type', '') === '') {
    $recommendations[] = "Configure organization type in Site Settings";
}

if (get_site_setting('organization_name', '') === '') {
    $recommendations[] = "Set organization name in Site Settings";
}

// Check for old hardcoded content in database
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM email_templates WHERE content LIKE '%Freedom Assembly Church%' OR content LIKE '%Church Team%' OR content LIKE '%Blessings,%'");
    $stmt->execute();
    $oldContentCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($oldContentCount > 0) {
        $recommendations[] = "Run migration script to update $oldContentCount email templates with old hardcoded content";
    }
} catch (PDOException $e) {
    $recommendations[] = "Could not check email templates for old content: " . $e->getMessage();
}

if (empty($recommendations)) {
    echo "<p class='success'>✓ System appears to be fully configured for organization-agnostic operation!</p>";
} else {
    echo "<p class='warning'>⚠ Recommendations for completing organization-agnostic setup:</p>";
    echo "<ul>";
    foreach ($recommendations as $rec) {
        echo "<li>$rec</li>";
    }
    echo "</ul>";
    echo "<p><strong>To run the migration script:</strong></p>";
    echo "<code>php tools/organization_agnostic_migration.php --execute</code>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Test Complete</h2>";
echo "<p>Test completed at " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='../admin/site_settings.php'>Go to Site Settings</a> to configure organization settings.</p>";
echo "</div>";

echo "</body></html>";
?>
