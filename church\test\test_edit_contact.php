<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Edit Contact</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Edit Contact Functionality</h1>
        
        <?php
        session_start();
        require_once '../config.php';
        $conn = $pdo;
        $_SESSION['admin_id'] = 1;
        
        // Create a test contact if needed
        $test_contact = null;
        try {
            // Check if test contact exists
            $stmt = $conn->prepare("SELECT * FROM contacts WHERE email LIKE 'edit_test_%' LIMIT 1");
            $stmt->execute();
            $test_contact = $stmt->fetch();
            
            if (!$test_contact) {
                // Create test contact
                $email = 'edit_test_' . time() . '@example.com';
                $name = 'Edit Test Contact';
                
                $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
                $stmt->execute([$email, $name]);
                $contact_id = $conn->lastInsertId();
                
                $test_contact = [
                    'id' => $contact_id,
                    'name' => $name,
                    'email' => $email
                ];
                
                echo "<div class='alert alert-success'>✅ Created test contact: $name (ID: $contact_id)</div>";
            }
        } catch (PDOException $e) {
            echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
        }
        
        if ($test_contact) {
            // Get available groups
            $stmt = $conn->prepare("SELECT * FROM contact_groups ORDER BY name");
            $stmt->execute();
            $groups = $stmt->fetchAll();
            
            echo "<div class='card'>";
            echo "<div class='card-header'>";
            echo "<h3>Test Contact: " . htmlspecialchars($test_contact['name']) . "</h3>";
            echo "</div>";
            echo "<div class='card-body'>";
            echo "<p><strong>ID:</strong> " . $test_contact['id'] . "</p>";
            echo "<p><strong>Email:</strong> " . htmlspecialchars($test_contact['email']) . "</p>";
            
            // Test edit button
            echo "<button type='button' class='btn btn-primary edit-contact' ";
            echo "data-id='" . $test_contact['id'] . "' ";
            echo "data-name='" . htmlspecialchars($test_contact['name']) . "' ";
            echo "data-email='" . htmlspecialchars($test_contact['email']) . "'>";
            echo "<i class='bi bi-pencil'></i> Test Edit";
            echo "</button>";
            
            echo "<a href='../admin/contacts.php' class='btn btn-secondary ms-2' target='_blank'>Go to Contacts Page</a>";
            echo "</div>";
            echo "</div>";
        }
        ?>
        
        <!-- Edit Contact Modal (copied from contacts.php) -->
        <div class="modal fade" id="editContactModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Contact</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editContactForm">
                            <input type="hidden" id="edit_contact_id">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">Name</label>
                                <input type="text" class="form-control" id="edit_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="edit_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="edit_email" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Groups</label>
                                <div id="groupCheckboxes">
                                    <?php if (!empty($groups)): ?>
                                        <?php foreach ($groups as $group): ?>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   name="groups[]"
                                                   value="<?php echo $group['id']; ?>" 
                                                   id="edit_group_<?php echo $group['id']; ?>">
                                            <label class="form-check-label" for="edit_group_<?php echo $group['id']; ?>">
                                                <?php echo htmlspecialchars($group['name']); ?>
                                            </label>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p class="text-muted">No groups available</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="saveContact">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Debug version of the edit functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up edit functionality...');
            
            // Handle edit contact buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.edit-contact')) {
                    console.log('Edit button clicked!');
                    e.preventDefault();
                    const button = e.target.closest('.edit-contact');
                    const contactId = button.getAttribute('data-id');
                    const contactName = button.getAttribute('data-name');
                    const contactEmail = button.getAttribute('data-email');
                    
                    console.log('Contact data:', { contactId, contactName, contactEmail });
                    
                    // Populate the edit modal
                    document.getElementById('edit_contact_id').value = contactId;
                    document.getElementById('edit_name').value = contactName;
                    document.getElementById('edit_email').value = contactEmail;
                    
                    // Load current groups for this contact
                    loadContactGroups(contactId);
                    
                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('editContactModal'));
                    modal.show();
                }
            });
            
            // Handle save contact button click
            const saveContactBtn = document.getElementById('saveContact');
            if (saveContactBtn) {
                console.log('Save button found, adding event listener...');
                saveContactBtn.addEventListener('click', function(e) {
                    console.log('Save button clicked!');
                    e.preventDefault();
                    
                    const contactId = document.getElementById('edit_contact_id').value;
                    const name = document.getElementById('edit_name').value.trim();
                    const email = document.getElementById('edit_email').value.trim();
                    
                    console.log('Form data:', { contactId, name, email });
                    
                    // Get selected groups
                    const selectedGroups = [];
                    const groupCheckboxes = document.querySelectorAll('#editContactModal input[name="groups[]"]:checked');
                    groupCheckboxes.forEach(checkbox => {
                        selectedGroups.push(checkbox.value);
                    });
                    
                    console.log('Selected groups:', selectedGroups);
                    
                    if (!name || !email) {
                        alert('Please fill in all required fields.');
                        return;
                    }
                    
                    // Disable button and show loading
                    saveContactBtn.disabled = true;
                    saveContactBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
                    
                    const updateData = {
                        id: contactId,
                        name: name,
                        email: email,
                        groups: selectedGroups
                    };
                    
                    console.log('Sending update request:', updateData);
                    
                    // Send update request
                    fetch('../admin/ajax/update_contact.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(updateData)
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Response data:', data);
                        if (data.success) {
                            alert('Contact updated successfully!');
                            // Close modal and reload page
                            const modal = bootstrap.Modal.getInstance(document.getElementById('editContactModal'));
                            modal.hide();
                            location.reload();
                        } else {
                            alert('Error updating contact: ' + (data.message || 'Unknown error'));
                            // Re-enable button
                            saveContactBtn.disabled = false;
                            saveContactBtn.innerHTML = 'Save Changes';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error updating contact. Please try again.');
                        // Re-enable button
                        saveContactBtn.disabled = false;
                        saveContactBtn.innerHTML = 'Save Changes';
                    });
                });
            } else {
                console.log('Save button not found!');
            }
        });
        
        // Function to load contact groups for editing
        function loadContactGroups(contactId) {
            console.log('Loading groups for contact:', contactId);
            fetch(`../admin/ajax/get_contact_groups.php?id=${contactId}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Groups response:', data);
                    // Uncheck all group checkboxes first
                    const groupCheckboxes = document.querySelectorAll('#editContactModal input[name="groups[]"]');
                    groupCheckboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });
                    
                    // The response is directly an array of group IDs
                    if (Array.isArray(data)) {
                        data.forEach(groupId => {
                            const checkbox = document.querySelector(`#edit_group_${groupId}`);
                            if (checkbox) {
                                checkbox.checked = true;
                                console.log('Checked group:', groupId);
                            }
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading contact groups:', error);
                });
        }
    </script>
</body>
</html>
