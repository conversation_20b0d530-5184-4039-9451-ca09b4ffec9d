<?php
// Debug version of event reports
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting debug...<br>";

session_start();
echo "Session started...<br>";

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "Not logged in, redirecting...<br>";
    header("Location: login.php");
    exit();
}
echo "User logged in...<br>";

// Include the configuration file
require_once '../config.php';
echo "Config loaded...<br>";

// Database connection - using the connection from config.php
$conn = $pdo;
echo "Database connected...<br>";

// Test organization name function
try {
    $orgName = get_organization_name();
    echo "Organization name: " . htmlspecialchars($orgName) . "<br>";
} catch (Exception $e) {
    echo "Error getting organization name: " . $e->getMessage() . "<br>";
}

// Test database query
try {
    $events_stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC LIMIT 3");
    $events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Found " . count($events) . " events<br>";
} catch (Exception $e) {
    echo "Error querying events: " . $e->getMessage() . "<br>";
}

// Set page variables
$page_title = "Event Reports Debug";
$page_header = "Event Reports Debug";
$page_description = "Debug event reports.";

echo "Page variables set...<br>";

// Include header
try {
    include 'includes/header.php';
    echo "Header included successfully<br>";
} catch (Exception $e) {
    echo "Error including header: " . $e->getMessage() . "<br>";
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Debug Event Reports</h5>
                </div>
                <div class="card-body">
                    <p>If you can see this, the basic page structure is working.</p>
                    <p>Organization: <?= htmlspecialchars(get_organization_name()) ?></p>
                    <p>Events found: <?= count($events ?? []) ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
try {
    include 'includes/footer.php';
    echo "Footer included successfully<br>";
} catch (Exception $e) {
    echo "Error including footer: " . $e->getMessage() . "<br>";
}
?>
