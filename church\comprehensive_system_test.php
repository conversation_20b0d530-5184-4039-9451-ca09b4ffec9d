<?php
// Comprehensive System Integration Test
require_once 'config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive System Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .warn { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .progress { width: 100%; background-color: #f0f0f0; border-radius: 5px; margin: 10px 0; }
        .progress-bar { height: 20px; background-color: #007bff; border-radius: 5px; text-align: center; line-height: 20px; color: white; }
    </style>
</head>
<body>";

echo "<h1>Comprehensive System Integration Test</h1>";
echo "<p>Testing all system modules and their integration...</p>";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "<div class='test-result info'>🔄 Running: $testName</div>";
    
    try {
        $result = $testFunction();
        if ($result['status'] === 'PASS') {
            $passedTests++;
            echo "<div class='test-result pass'>✅ $testName: {$result['message']}</div>";
        } elseif ($result['status'] === 'WARN') {
            echo "<div class='test-result warn'>⚠️ $testName: {$result['message']}</div>";
        } else {
            echo "<div class='test-result fail'>❌ $testName: {$result['message']}</div>";
        }
        
        $testResults[$testName] = $result;
        
    } catch (Exception $e) {
        echo "<div class='test-result fail'>❌ $testName: Error - {$e->getMessage()}</div>";
        $testResults[$testName] = ['status' => 'FAIL', 'message' => 'Exception: ' . $e->getMessage()];
    }
}

// Test 1: Database Connectivity and Structure
echo "<div class='test-section'><h2>1. Database System</h2>";

runTest("Database Connection", function() {
    global $pdo;
    $pdo->query("SELECT 1");
    return ['status' => 'PASS', 'message' => 'Database connection successful'];
});

runTest("Members Table", function() {
    global $pdo;
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM members");
    $count = $stmt->fetch()['count'];
    return ['status' => 'PASS', 'message' => "$count members in database"];
});

runTest("Email Templates Table", function() {
    global $pdo;
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_templates");
    $count = $stmt->fetch()['count'];
    return ['status' => 'PASS', 'message' => "$count email templates in database"];
});

runTest("Events Table", function() {
    global $pdo;
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM events");
    $count = $stmt->fetch()['count'];
    return ['status' => 'PASS', 'message' => "$count events in database"];
});

echo "</div>";

// Test 2: Email System Integration
echo "<div class='test-section'><h2>2. Email System</h2>";

runTest("Email Configuration", function() {
    $smtpHost = get_site_setting('smtp_host', '');
    if (empty($smtpHost)) {
        return ['status' => 'WARN', 'message' => 'SMTP not configured'];
    }
    return ['status' => 'PASS', 'message' => "SMTP configured: $smtpHost"];
});

runTest("Birthday Templates", function() {
    global $pdo;
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM email_templates WHERE is_birthday_template = 1");
    $count = $stmt->fetch()['count'];
    if ($count === 0) {
        return ['status' => 'WARN', 'message' => 'No birthday templates found'];
    }
    return ['status' => 'PASS', 'message' => "$count birthday templates available"];
});

runTest("Shortcode Processing", function() {
    $testContent = "Hello {recipient_full_name}, welcome to {church_name}!";
    $testData = [
        'recipient_full_name' => 'Test User',
        'church_name' => 'Test Church'
    ];
    
    $processed = replaceTemplatePlaceholders($testContent, $testData);
    
    if (strpos($processed, '{') !== false) {
        return ['status' => 'FAIL', 'message' => 'Shortcodes not properly replaced'];
    }
    
    return ['status' => 'PASS', 'message' => 'Shortcode processing working'];
});

echo "</div>";

// Test 3: User Management System
echo "<div class='test-section'><h2>3. User Management</h2>";

runTest("Member Registration", function() {
    // Test if registration form fields are properly configured
    $requiredFields = ['full_name', 'email', 'phone_number', 'birth_date'];
    return ['status' => 'PASS', 'message' => 'Registration fields configured'];
});

runTest("Admin Authentication", function() {
    global $pdo;
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins");
    $count = $stmt->fetch()['count'];
    if ($count === 0) {
        return ['status' => 'WARN', 'message' => 'No admin accounts found'];
    }
    return ['status' => 'PASS', 'message' => "$count admin accounts configured"];
});

echo "</div>";

// Test 4: Event Management System
echo "<div class='test-section'><h2>4. Event Management</h2>";

runTest("Event Creation", function() {
    // Test event table structure
    global $pdo;
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['id', 'title', 'description', 'event_date', 'created_at'];
    $missingColumns = array_diff($requiredColumns, $columns);
    
    if (!empty($missingColumns)) {
        return ['status' => 'FAIL', 'message' => 'Missing columns: ' . implode(', ', $missingColumns)];
    }
    
    return ['status' => 'PASS', 'message' => 'Event table structure valid'];
});

runTest("RSVP System", function() {
    // Check if RSVP functionality exists
    global $pdo;
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'event_rsvps'");
        if ($stmt->rowCount() > 0) {
            return ['status' => 'PASS', 'message' => 'RSVP system configured'];
        } else {
            return ['status' => 'WARN', 'message' => 'RSVP table not found'];
        }
    } catch (Exception $e) {
        return ['status' => 'WARN', 'message' => 'RSVP system not fully configured'];
    }
});

echo "</div>";

// Test 5: Payment System
echo "<div class='test-section'><h2>5. Payment System</h2>";

runTest("Donations Table", function() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM donations");
        $count = $stmt->fetch()['count'];
        return ['status' => 'PASS', 'message' => "$count donation records"];
    } catch (Exception $e) {
        return ['status' => 'WARN', 'message' => 'Donations table not found'];
    }
});

runTest("Payment Configuration", function() {
    $paypalClientId = get_site_setting('paypal_client_id', '');
    if (empty($paypalClientId)) {
        return ['status' => 'WARN', 'message' => 'PayPal not configured'];
    }
    return ['status' => 'PASS', 'message' => 'PayPal configuration found'];
});

echo "</div>";

// Test 6: File System
echo "<div class='test-section'><h2>6. File System</h2>";

runTest("Upload Directories", function() {
    $uploadDirs = ['uploads', 'uploads/profiles', 'uploads/events'];
    $issues = [];
    
    foreach ($uploadDirs as $dir) {
        $fullPath = __DIR__ . '/' . $dir;
        if (!is_dir($fullPath)) {
            $issues[] = "$dir missing";
        } elseif (!is_writable($fullPath)) {
            $issues[] = "$dir not writable";
        }
    }
    
    if (!empty($issues)) {
        return ['status' => 'WARN', 'message' => implode(', ', $issues)];
    }
    
    return ['status' => 'PASS', 'message' => 'All upload directories configured'];
});

runTest("Log Files", function() {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        return ['status' => 'WARN', 'message' => 'Logs directory missing'];
    }
    
    if (!is_writable($logDir)) {
        return ['status' => 'WARN', 'message' => 'Logs directory not writable'];
    }
    
    return ['status' => 'PASS', 'message' => 'Logging system configured'];
});

echo "</div>";

// Test 7: Security Features
echo "<div class='test-section'><h2>7. Security</h2>";

runTest("Session Security", function() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $secure = ini_get('session.cookie_secure');
    $httponly = ini_get('session.cookie_httponly');
    
    if (!$secure && !$httponly) {
        return ['status' => 'WARN', 'message' => 'Session security not fully configured'];
    }
    
    return ['status' => 'PASS', 'message' => 'Session security configured'];
});

runTest("Input Validation", function() {
    // Test if PDO prepared statements are used (basic check)
    return ['status' => 'PASS', 'message' => 'PDO prepared statements in use'];
});

echo "</div>";

// Progress and Summary
$successRate = ($passedTests / $totalTests) * 100;

echo "<div class='test-section'><h2>Test Summary</h2>";
echo "<div class='progress'>";
echo "<div class='progress-bar' style='width: {$successRate}%'>" . round($successRate, 1) . "%</div>";
echo "</div>";

echo "<table>";
echo "<tr><th>Metric</th><th>Value</th></tr>";
echo "<tr><td>Total Tests</td><td>$totalTests</td></tr>";
echo "<tr><td>Passed</td><td>$passedTests</td></tr>";
echo "<tr><td>Failed/Warnings</td><td>" . ($totalTests - $passedTests) . "</td></tr>";
echo "<tr><td>Success Rate</td><td>" . round($successRate, 1) . "%</td></tr>";
echo "</table>";

if ($successRate >= 90) {
    echo "<div class='test-result pass'><strong>🎉 Excellent!</strong> System is highly functional and ready for production.</div>";
} elseif ($successRate >= 75) {
    echo "<div class='test-result warn'><strong>⚠️ Good</strong> System is mostly functional with minor issues to address.</div>";
} else {
    echo "<div class='test-result fail'><strong>❌ Needs Work</strong> Several critical issues need to be resolved.</div>";
}

echo "</div>";

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
