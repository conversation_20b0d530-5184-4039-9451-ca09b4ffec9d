<?php
// Check birthday templates for issues
require_once 'config.php';

echo "<h1>Birthday Template Analysis</h1>\n";

try {
    // Get all birthday templates
    $stmt = $pdo->query("SELECT id, template_name, subject, content FROM email_templates WHERE is_birthday_template = 1");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Found " . count($templates) . " birthday templates:</h2>\n";
    
    foreach ($templates as $template) {
        echo "<div style='border: 1px solid #ccc; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: " . $template['id'] . ")</h3>\n";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>\n";
        
        // Check for images in content
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $matches);
        
        if (!empty($matches[1])) {
            echo "<h4>Images found in template:</h4>\n";
            echo "<ul>\n";
            foreach ($matches[1] as $imgSrc) {
                echo "<li>" . htmlspecialchars($imgSrc) . "</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>No images found in template content.</p>\n";
        }
        
        // Check for shortcodes
        preg_match_all('/{([^}]+)}/', $template['content'], $shortcodes);
        
        if (!empty($shortcodes[1])) {
            echo "<h4>Shortcodes found:</h4>\n";
            echo "<ul>\n";
            foreach (array_unique($shortcodes[1]) as $shortcode) {
                echo "<li>{" . htmlspecialchars($shortcode) . "}</li>\n";
            }
            echo "</ul>\n";
        }
        
        // Show first 500 characters of content
        echo "<h4>Content Preview:</h4>\n";
        echo "<div style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>\n";
        echo "<pre>" . htmlspecialchars(substr($template['content'], 0, 500)) . "...</pre>\n";
        echo "</div>\n";
        
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>\n";
}
?>
