<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Contact Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Final Contact Functionality Test</h1>
        
        <?php
        session_start();
        require_once '../config.php';
        $conn = $pdo;
        $_SESSION['admin_id'] = 1;
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        $action = $_GET['action'] ?? 'menu';
        
        if ($action === 'test_all') {
            echo "<h2>🧪 Running All Tests</h2>";
            
            // Test 1: Delete functionality
            echo "<h3>Test 1: Delete Functionality</h3>";
            echo "<p><a href='final_delete_test.php' target='_blank' class='btn btn-primary'>Run Delete Tests</a></p>";
            
            // Test 2: Create test data for edit testing
            echo "<h3>Test 2: Creating Test Data for Edit Testing</h3>";
            
            try {
                // Create test group if not exists
                $stmt = $conn->prepare("SELECT id FROM contact_groups WHERE name = 'Edit Test Group' LIMIT 1");
                $stmt->execute();
                $group_id = $stmt->fetchColumn();
                
                if (!$group_id) {
                    $stmt = $conn->prepare("INSERT INTO contact_groups (name, description) VALUES (?, ?)");
                    $stmt->execute(['Edit Test Group', 'Group for testing edit functionality']);
                    $group_id = $conn->lastInsertId();
                    echo "<div class='alert alert-success'>✅ Created test group (ID: $group_id)</div>";
                } else {
                    echo "<div class='alert alert-info'>ℹ️ Test group already exists (ID: $group_id)</div>";
                }
                
                // Create test contact
                $test_email = 'edit_test_final_' . time() . '@example.com';
                $test_name = 'Final Edit Test Contact';
                
                $stmt = $conn->prepare("INSERT INTO contacts (email, name, source) VALUES (?, ?, 'test')");
                $stmt->execute([$test_email, $test_name]);
                $contact_id = $conn->lastInsertId();
                
                // Add to group
                $stmt = $conn->prepare("INSERT INTO contact_group_members (contact_id, group_id) VALUES (?, ?)");
                $stmt->execute([$contact_id, $group_id]);
                
                echo "<div class='alert alert-success'>✅ Created test contact: $test_name (ID: $contact_id) - Added to group</div>";
                
                // Test 3: Direct AJAX test
                echo "<h3>Test 3: Direct AJAX Handler Test</h3>";
                
                // Test update_contact.php
                $update_data = [
                    'id' => $contact_id,
                    'name' => 'Updated Final Test Contact',
                    'email' => 'updated_final_test_' . time() . '@example.com',
                    'groups' => [$group_id]
                ];
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/ajax/update_contact.php');
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($update_data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen(json_encode($update_data))
                ]);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "<p><strong>Update AJAX Test:</strong></p>";
                echo "<p>HTTP Code: $http_code</p>";
                echo "<p>Response: " . htmlspecialchars($response) . "</p>";
                
                $result = json_decode($response, true);
                if ($result && $result['success']) {
                    echo "<div class='alert alert-success'>✅ Update AJAX handler works!</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ Update AJAX handler failed</div>";
                }
                
                // Test get_contact_groups.php
                $url = "http://localhost/campaign/church/admin/ajax/get_contact_groups.php?id=$contact_id";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "<p><strong>Get Groups AJAX Test:</strong></p>";
                echo "<p>HTTP Code: $http_code</p>";
                echo "<p>Response: " . htmlspecialchars($response) . "</p>";
                
                if ($http_code === 200) {
                    echo "<div class='alert alert-success'>✅ Get contact groups AJAX handler works!</div>";
                } else {
                    echo "<div class='alert alert-danger'>❌ Get contact groups AJAX handler failed</div>";
                }
                
                // Test 4: UI Test Instructions
                echo "<h3>Test 4: User Interface Testing</h3>";
                echo "<div class='card'>";
                echo "<div class='card-body'>";
                echo "<h5>Manual Testing Instructions:</h5>";
                echo "<ol>";
                echo "<li><a href='../admin/contacts.php' target='_blank' class='btn btn-sm btn-primary'>Open Contacts Page</a></li>";
                echo "<li>Find the test contact: <strong>Updated Final Test Contact</strong></li>";
                echo "<li>Click the <strong>pencil icon (edit button)</strong> - Modal should open with contact data</li>";
                echo "<li>Modify the name and/or email</li>";
                echo "<li>Check/uncheck group memberships</li>";
                echo "<li>Click <strong>Save Changes</strong> - Should show success and reload page</li>";
                echo "<li>Verify changes are reflected in the contact list</li>";
                echo "<li>Test individual delete by clicking the <strong>trash icon</strong></li>";
                echo "<li>Test bulk delete by selecting multiple contacts and clicking <strong>Delete Selected</strong></li>";
                echo "</ol>";
                echo "</div>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
            }
        } else {
            // Menu
            echo "<div class='row'>";
            echo "<div class='col-md-8'>";
            echo "<h2>Test Options</h2>";
            echo "<div class='list-group'>";
            echo "<a href='?action=test_all' class='list-group-item list-group-item-action'>";
            echo "<h5>🧪 Run All Tests</h5>";
            echo "<p>Comprehensive test of all contact functionality</p>";
            echo "</a>";
            echo "<a href='../admin/contacts.php' class='list-group-item list-group-item-action' target='_blank'>";
            echo "<h5>📋 Go to Contacts Page</h5>";
            echo "<p>Test functionality directly on the contacts page</p>";
            echo "</a>";
            echo "<a href='test_edit_contact.php' class='list-group-item list-group-item-action' target='_blank'>";
            echo "<h5>✏️ Test Edit Contact</h5>";
            echo "<p>Isolated test of edit contact functionality</p>";
            echo "</a>";
            echo "<a href='final_delete_test.php' class='list-group-item list-group-item-action' target='_blank'>";
            echo "<h5>🗑️ Test Delete Functionality</h5>";
            echo "<p>Test individual and bulk delete operations</p>";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            
            echo "<div class='col-md-4'>";
            echo "<h2>Status Summary</h2>";
            echo "<div class='card'>";
            echo "<div class='card-body'>";
            echo "<h6>Fixed Issues:</h6>";
            echo "<ul class='list-unstyled'>";
            echo "<li>✅ Individual delete functionality</li>";
            echo "<li>✅ Bulk delete functionality</li>";
            echo "<li>✅ Edit contact modal opening</li>";
            echo "<li>✅ Edit contact form submission</li>";
            echo "<li>✅ Group membership handling</li>";
            echo "<li>✅ Database column references</li>";
            echo "<li>✅ JavaScript event handlers</li>";
            echo "<li>✅ AJAX communication</li>";
            echo "</ul>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        ?>
        
        <div class="mt-4">
            <a href="?" class="btn btn-secondary">Back to Menu</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
