<?php
/**
 * Check Old vs New Image Paths
 * 
 * This script compares image paths between old and new users
 * to identify the format differences causing display issues
 */

// Start session
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Old vs New Image Paths Analysis</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-12'>
            <div class='card'>
                <div class='card-header bg-warning text-dark'>
                    <h5 class='mb-0'><i class='bi bi-search'></i> Old vs New Image Paths Analysis</h5>
                </div>
                <div class='card-body'>
";

try {
    // Get all members with image paths, ordered by creation date
    $stmt = $conn->prepare("SELECT id, full_name, email, image_path, created_at FROM members WHERE image_path IS NOT NULL AND image_path != '' ORDER BY created_at DESC");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($members) > 0) {
        echo "<h6>All Members with Image Paths (Newest First):</h6>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped table-sm'>";
        echo "<thead><tr><th>ID</th><th>Name</th><th>Email</th><th>Image Path</th><th>Created</th><th>Path Format</th><th>File Exists</th><th>Preview</th></tr></thead>";
        echo "<tbody>";
        
        $pathFormats = [];
        $workingPaths = [];
        $brokenPaths = [];
        
        foreach ($members as $member) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($member['id']) . "</td>";
            echo "<td>" . htmlspecialchars($member['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($member['email']) . "</td>";
            echo "<td><code>" . htmlspecialchars($member['image_path']) . "</code></td>";
            echo "<td>" . date('M d, Y', strtotime($member['created_at'])) . "</td>";
            
            // Analyze path format
            $dbPath = $member['image_path'];
            $pathFormat = 'UNKNOWN';
            
            if (strpos($dbPath, 'uploads/') === 0) {
                $pathFormat = 'uploads/filename';
            } elseif (strpos($dbPath, '/uploads/') !== false) {
                $pathFormat = '/uploads/filename';
            } elseif (preg_match('/^[a-f0-9]+\.(jpg|jpeg|png|gif)$/i', $dbPath)) {
                $pathFormat = 'filename_only';
            } elseif (strpos($dbPath, '../uploads/') === 0) {
                $pathFormat = '../uploads/filename';
            } else {
                $pathFormat = 'OTHER: ' . substr($dbPath, 0, 20) . '...';
            }
            
            echo "<td><span class='badge bg-info'>" . htmlspecialchars($pathFormat) . "</span></td>";
            
            // Track path formats
            if (!isset($pathFormats[$pathFormat])) {
                $pathFormats[$pathFormat] = 0;
            }
            $pathFormats[$pathFormat]++;
            
            // Test file existence with different path combinations
            $testPaths = [
                __DIR__ . '/../' . $dbPath,
                __DIR__ . '/../uploads/' . basename($dbPath),
                __DIR__ . '/../../' . $dbPath,
                __DIR__ . '/' . $dbPath,
            ];
            
            $fileExists = false;
            $workingPath = '';
            $webPath = '';
            
            foreach ($testPaths as $testPath) {
                if (file_exists($testPath)) {
                    $fileExists = true;
                    $workingPath = $testPath;
                    
                    // Convert to web path
                    if (strpos($testPath, __DIR__ . '/../uploads/') === 0) {
                        $webPath = '../uploads/' . basename($testPath);
                    } elseif (strpos($testPath, __DIR__ . '/../') === 0) {
                        $webPath = '../' . substr($testPath, strlen(__DIR__ . '/../'));
                    }
                    break;
                }
            }
            
            if ($fileExists) {
                echo "<td><span class='badge bg-success'>EXISTS</span><br><small>" . htmlspecialchars(basename($workingPath)) . "</small></td>";
                $workingPaths[] = $member;
                
                // Show preview
                echo "<td><img src='" . htmlspecialchars($webPath) . "' alt='Profile' style='width: 40px; height: 40px; object-fit: cover; border-radius: 50%;' onerror='this.src=\"../assets/img/default-profile.jpg\"'></td>";
            } else {
                echo "<td><span class='badge bg-danger'>MISSING</span></td>";
                $brokenPaths[] = $member;
                echo "<td><img src='../assets/img/default-profile.jpg' alt='Default' style='width: 40px; height: 40px; object-fit: cover; border-radius: 50%;'></td>";
            }
            
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
        
        // Analysis Summary
        echo "<hr><h6>Path Format Analysis:</h6>";
        echo "<div class='row'>";
        foreach ($pathFormats as $format => $count) {
            $badgeColor = 'secondary';
            if ($format === 'uploads/filename') $badgeColor = 'success';
            elseif ($format === 'filename_only') $badgeColor = 'warning';
            elseif (strpos($format, 'OTHER') === 0) $badgeColor = 'danger';
            
            echo "<div class='col-md-3 mb-2'>";
            echo "<div class='card bg-$badgeColor text-white'>";
            echo "<div class='card-body text-center p-2'>";
            echo "<h6>$count</h6>";
            echo "<small>" . htmlspecialchars($format) . "</small>";
            echo "</div></div></div>";
        }
        echo "</div>";
        
        // Recommendations
        echo "<hr><h6>Recommendations:</h6>";
        
        if (count($brokenPaths) > 0) {
            echo "<div class='alert alert-danger'>";
            echo "<h6>❌ " . count($brokenPaths) . " members have missing image files:</h6>";
            echo "<ul class='mb-0'>";
            foreach (array_slice($brokenPaths, 0, 5) as $member) {
                echo "<li>" . htmlspecialchars($member['full_name']) . " - <code>" . htmlspecialchars($member['image_path']) . "</code></li>";
            }
            if (count($brokenPaths) > 5) {
                echo "<li><em>... and " . (count($brokenPaths) - 5) . " more</em></li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
        // Check for specific users mentioned in the issue
        echo "<hr><h6>Specific Users Analysis:</h6>";
        $specificUsers = ['Ndivhuwo Machiba', 'Godwin Bointa', 'Jude Mike'];
        
        foreach ($specificUsers as $userName) {
            $userFound = false;
            foreach ($members as $member) {
                if (stripos($member['full_name'], $userName) !== false) {
                    $userFound = true;
                    echo "<div class='alert alert-info'>";
                    echo "<strong>" . htmlspecialchars($member['full_name']) . "</strong><br>";
                    echo "Path: <code>" . htmlspecialchars($member['image_path']) . "</code><br>";
                    echo "Created: " . date('M d, Y H:i', strtotime($member['created_at'])) . "<br>";
                    
                    // Test this specific user's image
                    $testPath = __DIR__ . '/../uploads/' . basename($member['image_path']);
                    if (file_exists($testPath)) {
                        echo "File Status: <span class='badge bg-success'>EXISTS</span> - " . htmlspecialchars(basename($testPath));
                    } else {
                        echo "File Status: <span class='badge bg-danger'>MISSING</span>";
                    }
                    echo "</div>";
                    break;
                }
            }
            
            if (!$userFound) {
                echo "<div class='alert alert-warning'>User '$userName' not found in database</div>";
            }
        }
        
    } else {
        echo "<div class='alert alert-info'>No members with image paths found in database</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Database Error: " . htmlspecialchars($e->getMessage()) . "</div>";
}

echo "
                    <div class='d-grid gap-2 mt-4'>
                        <a href='fix_image_paths.php' class='btn btn-warning'>Fix Database Paths</a>
                        <a href='members.php' class='btn btn-primary'>Back to Members</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
