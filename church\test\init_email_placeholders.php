<?php
// <PERSON><PERSON><PERSON> to initialize email placeholders in the system
require_once __DIR__ . '/../config.php';

try {
    // Initialize reply_to_email if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_settings WHERE setting_key = 'reply_to_email'");
    $stmt->execute();
    $exists = $stmt->fetchColumn();
    
    if (!$exists) {
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES ('reply_to_email', '<EMAIL>')");
        $stmt->execute();
        echo "<p>Added reply_to_email setting to email_settings table</p>";
    } else {
        echo "<p>reply_to_email setting already exists in email_settings table</p>";
    }
    
    // Register the random number placeholders in the system
    function add_placeholder_function($placeholder_key, $function_code) {
        global $pdo;
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM email_placeholder_functions WHERE placeholder_key = ?");
        $stmt->execute([$placeholder_key]);
        $exists = $stmt->fetchColumn();
        
        if (!$exists) {
            $stmt = $pdo->prepare("INSERT INTO email_placeholder_functions (placeholder_key, function_code) VALUES (?, ?)");
            $stmt->execute([$placeholder_key, $function_code]);
            return true;
        }
        return false;
    }
    
    // Try to create the email_placeholder_functions table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS email_placeholder_functions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        placeholder_key VARCHAR(255) NOT NULL,
        function_code TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // Add random number generator placeholders
    $random_functions = [
        'random_number_1' => 'return rand(1, 20);',
        'random_number_2' => 'return rand(21, 40);',
        'random_number_3' => 'return rand(41, 60);',
        'random_number_4' => 'return rand(61, 80);',
        'random_number_5' => 'return rand(81, 99);',
        'random_powerball' => 'return rand(1, 15);'
    ];
    
    $added_count = 0;
    foreach ($random_functions as $key => $code) {
        if (add_placeholder_function($key, $code)) {
            $added_count++;
        }
    }
    
    echo "<p>Added $added_count new placeholder functions for random numbers</p>";
    
    // Verify the reply_to_email value
    $stmt = $pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'reply_to_email'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "<p>Current reply_to_email value: " . htmlspecialchars($result['setting_value']) . "</p>";
    }
    
    echo "<p>Placeholder initialization completed</p>";
    
} catch (PDOException $e) {
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}