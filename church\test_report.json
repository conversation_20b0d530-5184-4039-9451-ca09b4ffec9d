{"summary": {"total": 118, "passed": 2, "failed": 116, "warnings": 0, "duration": 0.3569612503051758, "success_rate": 1.694915254237288}, "results": [{"test": "Database Connection", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Members Table Check", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Email Templates Table Check", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "API Database Check", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "API Members Check", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Registration Page Access", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "User Login <PERSON>", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "User Registration Page", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Password Reset Page", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "<PERSON><PERSON>", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /index.php", "status": "PASS", "message": "Status: 200", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /events.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /event_detail.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /donate.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /register.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/login.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/register.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/dashboard.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/profile.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/events.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/event_detail.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/birthday_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/send_birthday_message.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/change_password.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/forgot_password.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/login.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/dashboard.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/members.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/add_member.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/automated_email_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/birthday.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/bulk_email.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_scheduler.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/appearance_settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/site_settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/security_settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/events.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/donations.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/payment_integration.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/contacts.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/contact_groups.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/whatsapp_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/whatsapp_messages.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_analytics.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/about_shortcodes.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/test_birthday_email.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/debug_images.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/events.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /events.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /event_detail.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/events.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/event_detail.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "RSVP Handler", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "User RSVP Handler", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/members.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/add_member.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/edit_member.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/view_member.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /user/profile.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/automated_email_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/bulk_email.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_scheduler.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/email_analytics.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/test_birthday_email.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Template Analysis", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Shortcode Validation", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Birthday Fixes Test", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Quick System Validation", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "<PERSON><PERSON> Templates Page", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Shortcode Validation", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Birthday Template Check", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Birthday Email Test Page", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /donate.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /process_donation.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/donations.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /admin/payment_integration.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /donation_success.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:12"}, {"test": "Page Access: /donation_error.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /complete_paypal_payment.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/whatsapp_templates.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/whatsapp_messages.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/send_birthday_notification.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /user/send_birthday_message.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Upload Directory /uploads", "status": "FAIL", "message": "Status: 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Upload Directory /uploads/profiles", "status": "FAIL", "message": "Status: 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Upload Directory /uploads/events", "status": "FAIL", "message": "Status: 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/debug_images.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/test_image_paths.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/debug_member_image.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/security_settings.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /admin/security_audit.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /test_2fa.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /api/birthdays.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /api/check_database.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /api/check_members_table.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /ajax/get_recipient_details.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /ajax/send_single_email.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /user/ajax/get_event_details.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /check_database.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /check_smtp.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /phpinfo.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /production_readiness_check.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /comprehensive_system_test.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Hardcoded Localhost Check /", "status": "PASS", "message": "No localhost references", "timestamp": "2025-06-27 21:08:13"}, {"test": "Appearance Settings Page", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /cron/birthday_reminders.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /cron/event_reminders.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /cron/process_birthday_reminders.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /cron/process_email_queue.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /comprehensive_test.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /test_system.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}, {"test": "Page Access: /final_test.php", "status": "FAIL", "message": "Expected 200, got 404", "timestamp": "2025-06-27 21:08:13"}]}