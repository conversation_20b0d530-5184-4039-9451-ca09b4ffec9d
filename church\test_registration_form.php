<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Form Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .preview-image { max-width: 200px; margin-top: 15px; border-radius: 10px; }
        .test-section { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Registration Form Upload Test</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            echo '<div class="test-section">';
            echo '<h3>Form Submission Results:</h3>';
            
            echo '<h4>POST Data:</h4>';
            echo '<pre>' . print_r($_POST, true) . '</pre>';
            
            echo '<h4>FILES Data:</h4>';
            echo '<pre>' . print_r($_FILES, true) . '</pre>';
            
            if (isset($_FILES['profile_image'])) {
                $file = $_FILES['profile_image'];
                if ($file['error'] === UPLOAD_ERR_OK) {
                    echo '<p style="color: green;"><strong>✅ File upload successful!</strong></p>';
                    echo '<p>File name: ' . htmlspecialchars($file['name']) . '</p>';
                    echo '<p>File size: ' . $file['size'] . ' bytes</p>';
                    echo '<p>File type: ' . htmlspecialchars($file['type']) . '</p>';
                } else {
                    echo '<p style="color: red;"><strong>❌ File upload error:</strong> ' . $file['error'] . '</p>';
                }
            } else {
                echo '<p style="color: red;"><strong>❌ No file data received</strong></p>';
            }
            echo '</div>';
        }
        ?>
        
        <div class="test-section">
            <h3>Simple Upload Test</h3>
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="profile_image" class="form-label">Profile Image *</label>
                    <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*" required onchange="previewImage(this)">
                    <img id="preview" class="preview-image d-none">
                    <small class="form-text text-muted">Select an image file to test upload functionality.</small>
                </div>
                
                <div class="mb-3">
                    <label for="test_name" class="form-label">Test Name</label>
                    <input type="text" class="form-control" id="test_name" name="test_name" value="Test User" required>
                </div>
                
                <button type="submit" class="btn btn-primary">Test Submit</button>
            </form>
        </div>
        
        <div class="test-section">
            <h3>File Input Debug Info</h3>
            <button onclick="debugFileInput()" class="btn btn-secondary">Debug File Input</button>
            <div id="debug-output" class="mt-3"></div>
        </div>
        
        <div class="test-section">
            <h3>Browser Compatibility Check</h3>
            <div id="browser-info"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const preview = document.getElementById('preview');
            const file = input.files[0];
            
            console.log('previewImage called', file);

            // Clear any previous validation messages
            input.setCustomValidity('');

            if (file) {
                console.log('File selected:', file.name, file.size, file.type);
                
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    input.setCustomValidity('Please select a valid image file (JPG, PNG, or GIF)');
                    preview.classList.add('d-none');
                    console.log('Invalid file type:', file.type);
                    return;
                }

                // Validate file size (5MB max)
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes
                if (file.size > maxSize) {
                    input.setCustomValidity('Image file size must be less than 5MB');
                    preview.classList.add('d-none');
                    console.log('File too large:', file.size);
                    return;
                }

                // Show preview if validation passes
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.classList.remove('d-none');
                    console.log('Preview loaded successfully');
                }
                reader.readAsDataURL(file);
            } else {
                preview.classList.add('d-none');
                console.log('No file selected');
            }
        }
        
        function debugFileInput() {
            const fileInput = document.getElementById('profile_image');
            const output = document.getElementById('debug-output');
            
            let info = '<h5>File Input Debug:</h5>';
            info += '<p><strong>Element exists:</strong> ' + (fileInput ? 'Yes' : 'No') + '</p>';
            
            if (fileInput) {
                info += '<p><strong>Type:</strong> ' + fileInput.type + '</p>';
                info += '<p><strong>Name:</strong> ' + fileInput.name + '</p>';
                info += '<p><strong>Accept:</strong> ' + fileInput.accept + '</p>';
                info += '<p><strong>Required:</strong> ' + fileInput.required + '</p>';
                info += '<p><strong>Disabled:</strong> ' + fileInput.disabled + '</p>';
                info += '<p><strong>Files count:</strong> ' + (fileInput.files ? fileInput.files.length : 'No files property') + '</p>';
                
                if (fileInput.files && fileInput.files.length > 0) {
                    info += '<p><strong>Selected file:</strong> ' + fileInput.files[0].name + '</p>';
                }
                
                info += '<p><strong>Computed style display:</strong> ' + window.getComputedStyle(fileInput).display + '</p>';
                info += '<p><strong>Computed style visibility:</strong> ' + window.getComputedStyle(fileInput).visibility + '</p>';
            }
            
            output.innerHTML = info;
        }
        
        // Browser compatibility check
        document.addEventListener('DOMContentLoaded', function() {
            const browserInfo = document.getElementById('browser-info');
            let info = '<h5>Browser Support:</h5>';
            info += '<p><strong>File API:</strong> ' + (window.File ? 'Supported' : 'Not supported') + '</p>';
            info += '<p><strong>FileReader:</strong> ' + (window.FileReader ? 'Supported' : 'Not supported') + '</p>';
            info += '<p><strong>FormData:</strong> ' + (window.FormData ? 'Supported' : 'Not supported') + '</p>';
            info += '<p><strong>User Agent:</strong> ' + navigator.userAgent + '</p>';
            browserInfo.innerHTML = info;
        });
        
        // Form submission debug
        document.querySelector('form').addEventListener('submit', function(e) {
            console.log('Form submission started');
            const fileInput = document.getElementById('profile_image');
            console.log('File input files:', fileInput.files);
            console.log('File input value:', fileInput.value);
            
            if (!fileInput.files || fileInput.files.length === 0) {
                console.log('No file selected - preventing submission');
                e.preventDefault();
                alert('Please select a file first!');
                return false;
            }
            
            console.log('File selected, allowing submission');
        });
    </script>
</body>
</html>
