<?php
/**
 * BirthdayReminder Class
 * 
 * Handles birthday email functionality including sending reminders
 * and managing birthday notifications.
 */

class BirthdayReminder {
    private $pdo;
    private $adminEmail;
    private $failedEmails = [];
    private $sentEmails = [];
    private $trackingEnabled = true;
    private $emailType = 'birthday';

    public function __construct($pdo, $adminEmail = null) {
        $this->pdo = $pdo;
        $this->adminEmail = $adminEmail ?: '<EMAIL>';
    }

    public function getUpcomingBirthdays($daysAhead) {
        // This query compares only month and day parts of the dates
        $query = "SELECT * FROM members WHERE 
                  MONTH(DATE_ADD(CURRENT_DATE, INTERVAL ? DAY)) = MONTH(birth_date) AND 
                  DAY(DATE_ADD(CURRENT_DATE, INTERVAL ? DAY)) = DAY(birth_date)";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$daysAhead, $daysAhead]);
        
        $members = $stmt->fetchAll();
        error_log("Found " . count($members) . " members with birthdays " . ($daysAhead == 0 ? "today" : "in $daysAhead days"));
        return $members;
    }

    public function sendBirthdayEmails($templateId = null) {
        $this->failedEmails = [];
        $this->sentEmails = [];
        
        try {
            $totalSent = 0;
            $totalFailed = 0;
            
            // Get members with birthdays today
            $members = $this->getUpcomingBirthdays(0);
            
            foreach ($members as $member) {
                if (empty($member['email'])) {
                    continue;
                }
                
                // Get template
                if ($templateId) {
                    $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
                    $stmt->execute([$templateId]);
                    $template = $stmt->fetch();
                } else {
                    $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 1 ORDER BY RAND() LIMIT 1");
                    $stmt->execute();
                    $template = $stmt->fetch();
                }
                
                if (!$template) {
                    error_log("No birthday template found");
                    continue;
                }
                
                // Send email
                $subject = replaceTemplatePlaceholders($template['subject'], $member);
                $body = replaceTemplatePlaceholders($template['content'], $member);
                
                if (sendEmail($member['email'], $member['full_name'], $subject, $body)) {
                    $totalSent++;
                    $this->sentEmails[] = [
                        'member' => $member['full_name'],
                        'email' => $member['email']
                    ];
                } else {
                    $totalFailed++;
                    $this->failedEmails[] = [
                        'member' => $member['full_name'],
                        'email' => $member['email'],
                        'error' => 'Failed to send email'
                    ];
                }
            }
            
            return [
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed,
                'sent' => $this->sentEmails,
                'failed' => $this->failedEmails
            ];
            
        } catch (Exception $e) {
            error_log("Error in sendBirthdayEmails: " . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    public function sendBirthdayReminders($templateId = null, $daysAhead = 3) {
        $this->failedEmails = [];
        $this->sentEmails = [];
        
        try {
            $totalSent = 0;
            $totalFailed = 0;
            
            // Get members with upcoming birthdays
            $members = $this->getUpcomingBirthdays($daysAhead);
            
            foreach ($members as $member) {
                if (empty($member['email'])) {
                    continue;
                }
                
                // Get template
                if ($templateId) {
                    $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
                    $stmt->execute([$templateId]);
                    $template = $stmt->fetch();
                } else {
                    $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 0 ORDER BY RAND() LIMIT 1");
                    $stmt->execute();
                    $template = $stmt->fetch();
                }
                
                if (!$template) {
                    error_log("No reminder template found");
                    continue;
                }
                
                // Add days information to member data
                $member['days_until_birthday'] = $daysAhead;
                
                // Enhanced birthday placeholder calculations
                // Calculate birth date components
                $birth_month = date('m', strtotime($member['birth_date'] ?? 'now'));
                $birth_day = date('d', strtotime($member['birth_date'] ?? 'now'));
                $birth_year = date('Y', strtotime($member['birth_date'] ?? 'now'));
                $current_year = date('Y');
                $next_year = $current_year + 1;
                
                // Determine if birthday has passed this year already
                $this_year_birthday = $current_year . '-' . $birth_month . '-' . $birth_day;
                $next_year_birthday = $next_year . '-' . $birth_month . '-' . $birth_day;
                
                // Calculate upcoming birthday date and age
                $upcoming_date = strtotime($this_year_birthday) < time() ? $next_year_birthday : $this_year_birthday;
                $age = strtotime($this_year_birthday) < time() ? 
                       ($next_year - $birth_year) : 
                       ($current_year - $birth_year);
                
                // Prepare comprehensive set of placeholder values
                $member['birthday_date'] = date('F j', strtotime($member['birth_date'] ?? 'now'));
                $member['birthday_year'] = $birth_year;
                $member['current_year'] = $current_year;
                $member['current_date'] = date('F j, Y');
                $member['current_time'] = date('g:i A');
                $member['upcoming_birthday_date'] = date('F j, Y', strtotime($upcoming_date));
                $member['upcoming_birthday_day'] = date('l', strtotime($upcoming_date));
                $member['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($upcoming_date));
                $member['age'] = $age;
                $member['birthday_member_age'] = $age;
                $member['days_text'] = $daysAhead == 0 ? 'today' : 
                                      ($daysAhead == 1 ? 'tomorrow' : 
                                      "in $daysAhead days");
                
                // Add recipient-specific placeholders
                if (!isset($member['first_name']) && isset($member['full_name'])) {
                    $nameParts = explode(' ', $member['full_name'], 2);
                    $member['first_name'] = $nameParts[0];
                    $member['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
                }
                
                $member['recipient_full_name'] = $member['full_name'] ?? '';
                $member['recipient_first_name'] = $member['first_name'] ?? '';
                $member['recipient_email'] = $member['email'] ?? '';
                $member['recipient_phone'] = $member['phone_number'] ?? '';
                
                // Also add these placeholders to "birthday_member_" equivalents
                $member['birthday_member_birth_date'] = $member['birthday_date'];
                $member['birthday_member_name'] = $member['first_name'] ?? '';
                $member['birthday_member_full_name'] = $member['full_name'] ?? '';
                
                // Send email
                $subject = replaceTemplatePlaceholders($template['subject'], $member);
                $body = replaceTemplatePlaceholders($template['content'], $member);
                
                if (sendEmail($member['email'], $member['full_name'], $subject, $body)) {
                    $totalSent++;
                    $this->sentEmails[] = [
                        'member' => $member['full_name'],
                        'email' => $member['email']
                    ];
                } else {
                    $totalFailed++;
                    $this->failedEmails[] = [
                        'member' => $member['full_name'],
                        'email' => $member['email'],
                        'error' => 'Failed to send email'
                    ];
                }
            }
            
            return [
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed,
                'sent' => $this->sentEmails,
                'failed' => $this->failedEmails
            ];
            
        } catch (Exception $e) {
            error_log("Error in sendBirthdayReminders: " . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
} 