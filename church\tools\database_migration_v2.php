<?php
/**
 * Database Migration Script v2.0
 * Organization-Agnostic System Migration
 * 
 * This script migrates existing church management installations to the new
 * organization-agnostic system by:
 * 1. Adding new organization settings to the database
 * 2. Converting hardcoded references to dynamic placeholders
 * 3. Setting up default organization configuration
 * 
 * Usage: php database_migration_v2.php [--execute] [--org-type=church]
 */

require_once '../config.php';

// Configuration
$dryRun = true;
$defaultOrgType = 'church';
$verboseOutput = true;

// Process command line arguments
if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--execute') {
            $dryRun = false;
        }
        if ($arg === '--quiet') {
            $verboseOutput = false;
        }
        if (strpos($arg, '--org-type=') === 0) {
            $defaultOrgType = substr($arg, 11);
        }
    }
}

echo "=== Database Migration Script v2.0 ===\n";
echo "Organization-Agnostic System Migration\n";
echo "Mode: " . ($dryRun ? "DRY RUN (use --execute to apply changes)" : "EXECUTING CHANGES") . "\n";
echo "Default Organization Type: $defaultOrgType\n";
echo "Verbose: " . ($verboseOutput ? "ON" : "OFF") . "\n\n";

// Step 1: Add organization settings
echo "=== Step 1: Adding Organization Settings ===\n";

$organizationSettings = [
    'organization_type' => $defaultOrgType,
    'organization_name' => get_site_setting('site_title', 'Organization'),
    'organization_mission' => '',
    'organization_vision' => '',
    'organization_values' => '',
    'member_term' => 'Member',
    'leader_term' => 'Leader',
    'group_term' => 'Group',
    'event_term' => 'Event',
    'donation_term' => 'Donation'
];

// Set defaults based on organization type
if ($defaultOrgType === 'church') {
    $organizationSettings['member_term'] = 'Member';
    $organizationSettings['leader_term'] = 'Pastor';
    $organizationSettings['group_term'] = 'Ministry';
    $organizationSettings['event_term'] = 'Service';
    $organizationSettings['donation_term'] = 'Offering';
} elseif ($defaultOrgType === 'school') {
    $organizationSettings['member_term'] = 'Student';
    $organizationSettings['leader_term'] = 'Principal';
    $organizationSettings['group_term'] = 'Class';
    $organizationSettings['event_term'] = 'Assembly';
    $organizationSettings['donation_term'] = 'Fee';
} elseif ($defaultOrgType === 'business') {
    $organizationSettings['member_term'] = 'Employee';
    $organizationSettings['leader_term'] = 'Manager';
    $organizationSettings['group_term'] = 'Department';
    $organizationSettings['event_term'] = 'Meeting';
    $organizationSettings['donation_term'] = 'Contribution';
}

$settingsAdded = 0;
$settingsUpdated = 0;

foreach ($organizationSettings as $key => $defaultValue) {
    try {
        // Check if setting already exists
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            if ($verboseOutput) {
                echo "Setting '$key' already exists with value: " . $existing['setting_value'] . "\n";
            }
        } else {
            if (!$dryRun) {
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)");
                $stmt->execute([$key, $defaultValue]);
                $settingsAdded++;
            }
            
            if ($verboseOutput) {
                echo "Adding setting '$key' = '$defaultValue'" . ($dryRun ? " (DRY RUN)" : "") . "\n";
            }
        }
    } catch (PDOException $e) {
        echo "Error processing setting '$key': " . $e->getMessage() . "\n";
    }
}

echo "Settings added: $settingsAdded\n";

// Step 2: Update email templates
echo "\n=== Step 2: Updating Email Templates ===\n";

$templateReplacements = [
    // Organization references
    'Freedom Assembly Church International' => '{organization_name}',
    'Freedom Assembly Church' => '{organization_name}',
    'FACI' => '{organization_name}',
    
    // Team references
    'Church Team' => '{organization_name} Team',
    'church team' => '{organization_name} team',
    
    // Community references
    'church family' => '{organization_type} community',
    'Church Family' => '{organization_type} Community',
    'church community' => '{organization_type} community',
    'Church Community' => '{organization_type} Community',
    
    // Organization references
    'our church' => 'our {organization_type}',
    'Our church' => 'Our {organization_type}',
    'Our Church' => 'Our {organization_type}',
    'the church' => 'the {organization_type}',
    'The church' => 'The {organization_type}',
    'The Church' => 'The {organization_type}',
    
    // Greeting updates
    'Blessings,' => 'Best regards,',
    'God bless,' => 'Best regards,',
    'In Christ,' => 'Sincerely,',
    'Grace and peace,' => 'Best regards,',
    
    // Role references
    'Pastor' => '{leader_term}',
    'pastor' => '{leader_term}',
    'our pastor' => 'our {leader_term}',
    'Our pastor' => 'Our {leader_term}',
    'Our Pastor' => 'Our {leader_term}',
    
    // Member references
    'church member' => '{member_term}',
    'Church member' => '{member_term}',
    'Church Member' => '{member_term}',
    'congregation member' => '{member_term}',
    'Congregation member' => '{member_term}',
    'Congregation Member' => '{member_term}',
    
    // Service/Event references
    'church service' => '{event_term}',
    'Church service' => '{event_term}',
    'Church Service' => '{event_term}',
    'worship service' => '{event_term}',
    'Worship service' => '{event_term}',
    'Worship Service' => '{event_term}',
    
    // Ministry/Group references
    'ministry' => '{group_term}',
    'Ministry' => '{group_term}',
    'ministries' => '{group_term}s',
    'Ministries' => '{group_term}s',
    
    // Offering/Donation references
    'offering' => '{donation_term}',
    'Offering' => '{donation_term}',
    'offerings' => '{donation_term}s',
    'Offerings' => '{donation_term}s',
    'church offering' => '{donation_term}',
    'Church offering' => '{donation_term}',
    'Church Offering' => '{donation_term}',
];

$templatesUpdated = 0;

try {
    $stmt = $pdo->prepare("SELECT id, template_name, subject, content FROM email_templates");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($templates as $template) {
        $originalSubject = $template['subject'];
        $originalContent = $template['content'];
        $updatedSubject = $originalSubject;
        $updatedContent = $originalContent;
        $hasChanges = false;
        
        // Apply replacements
        foreach ($templateReplacements as $search => $replace) {
            if (strpos($updatedSubject, $search) !== false) {
                $updatedSubject = str_replace($search, $replace, $updatedSubject);
                $hasChanges = true;
            }
            if (strpos($updatedContent, $search) !== false) {
                $updatedContent = str_replace($search, $replace, $updatedContent);
                $hasChanges = true;
            }
        }
        
        if ($hasChanges) {
            if (!$dryRun) {
                $updateStmt = $pdo->prepare("UPDATE email_templates SET subject = ?, content = ? WHERE id = ?");
                $updateStmt->execute([$updatedSubject, $updatedContent, $template['id']]);
            }
            
            $templatesUpdated++;
            if ($verboseOutput) {
                echo "Updated template: " . $template['template_name'] . ($dryRun ? " (DRY RUN)" : "") . "\n";
            }
        }
    }
} catch (PDOException $e) {
    echo "Error updating email templates: " . $e->getMessage() . "\n";
}

echo "Email templates updated: $templatesUpdated\n";

// Step 3: Update WhatsApp templates
echo "\n=== Step 3: Updating WhatsApp Templates ===\n";

$whatsappUpdated = 0;

try {
    $stmt = $pdo->prepare("SELECT id, template_name, message_content FROM whatsapp_templates");
    $stmt->execute();
    $whatsappTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($whatsappTemplates as $template) {
        $originalContent = $template['message_content'];
        $updatedContent = $originalContent;
        $hasChanges = false;
        
        // Apply replacements
        foreach ($templateReplacements as $search => $replace) {
            if (strpos($updatedContent, $search) !== false) {
                $updatedContent = str_replace($search, $replace, $updatedContent);
                $hasChanges = true;
            }
        }
        
        if ($hasChanges) {
            if (!$dryRun) {
                $updateStmt = $pdo->prepare("UPDATE whatsapp_templates SET message_content = ? WHERE id = ?");
                $updateStmt->execute([$updatedContent, $template['id']]);
            }
            
            $whatsappUpdated++;
            if ($verboseOutput) {
                echo "Updated WhatsApp template: " . $template['template_name'] . ($dryRun ? " (DRY RUN)" : "") . "\n";
            }
        }
    }
} catch (PDOException $e) {
    echo "Error updating WhatsApp templates: " . $e->getMessage() . "\n";
}

echo "WhatsApp templates updated: $whatsappUpdated\n";

// Step 4: Create backup
if (!$dryRun) {
    echo "\n=== Step 4: Creating Backup ===\n";
    
    $backupFile = __DIR__ . '/migration_backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    try {
        $backupSql = "-- Migration Backup " . date('Y-m-d H:i:s') . "\n\n";
        
        // Backup settings
        $stmt = $pdo->prepare("SELECT * FROM settings WHERE setting_key LIKE 'organization_%' OR setting_key IN ('member_term', 'leader_term', 'group_term', 'event_term', 'donation_term')");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $backupSql .= "-- Organization Settings\n";
        foreach ($settings as $setting) {
            $backupSql .= "INSERT INTO settings (setting_key, setting_value) VALUES ('" . 
                         addslashes($setting['setting_key']) . "', '" . 
                         addslashes($setting['setting_value']) . "');\n";
        }
        
        file_put_contents($backupFile, $backupSql);
        echo "Backup created: $backupFile\n";
    } catch (Exception $e) {
        echo "Warning: Could not create backup: " . $e->getMessage() . "\n";
    }
}

// Summary
echo "\n=== Migration Summary ===\n";
echo "Organization settings added: $settingsAdded\n";
echo "Email templates updated: $templatesUpdated\n";
echo "WhatsApp templates updated: $whatsappUpdated\n";

if ($dryRun) {
    echo "\nThis was a DRY RUN. No changes were made to the database.\n";
    echo "To execute the migration, run: php database_migration_v2.php --execute\n";
    echo "To specify organization type: php database_migration_v2.php --execute --org-type=school\n";
} else {
    echo "\nMigration completed successfully!\n";
    echo "Next steps:\n";
    echo "1. Visit the admin Site Settings page to configure your organization details\n";
    echo "2. Test the system with different organization types\n";
    echo "3. Run the test script: php ../test/test_organization_agnostic.php\n";
}

echo "\n=== Migration Complete ===\n";
?>
